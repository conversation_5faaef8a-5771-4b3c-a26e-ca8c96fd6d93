using Xunit;
using FluentAssertions;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Tests.Unit.Core.ValueObjects;

/// <summary>
/// Unit tests for EnvironmentVariable value object
/// </summary>
public class EnvironmentVariableTests
{
    [Fact]
    public void Create_WithValidNameAndValue_ShouldReturnEnvironmentVariable()
    {
        // Arrange
        var name = "DATABASE_URL";
        var value = "postgresql://localhost:5432/mydb";

        // Act
        var result = EnvironmentVariable.Create(name, value);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be(name);
        result.Value.Should().Be(value);
        result.IsSensitive.Should().BeFalse();
    }

    [Fact]
    public void Create_WithSensitiveFlag_ShouldMarkAsSensitive()
    {
        // Arrange
        var name = "API_KEY";
        var value = "secret-key-123";

        // Act
        var result = EnvironmentVariable.Create(name, value, isSensitive: true);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be(name);
        result.Value.Should().Be(value);
        result.IsSensitive.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidName_ShouldThrowArgumentException(string invalidName)
    {
        // Act & Assert
        var action = () => EnvironmentVariable.Create(invalidName, "value");
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Create_WithNullValue_ShouldUseEmptyString()
    {
        // Arrange
        var name = "EMPTY_VAR";

        // Act
        var result = EnvironmentVariable.Create(name, null);

        // Assert
        result.Value.Should().Be(string.Empty);
    }

    [Fact]
    public void FromString_WithValidFormat_ShouldParseCorrectly()
    {
        // Arrange
        var envString = "DATABASE_URL=postgresql://localhost:5432/mydb";

        // Act
        var result = EnvironmentVariable.FromString(envString);

        // Assert
        result.Name.Should().Be("DATABASE_URL");
        result.Value.Should().Be("postgresql://localhost:5432/mydb");
        result.IsSensitive.Should().BeFalse();
    }

    [Fact]
    public void FromString_WithNoValue_ShouldUseEmptyValue()
    {
        // Arrange
        var envString = "EMPTY_VAR";

        // Act
        var result = EnvironmentVariable.FromString(envString);

        // Assert
        result.Name.Should().Be("EMPTY_VAR");
        result.Value.Should().Be(string.Empty);
    }

    [Fact]
    public void FromString_WithSensitiveFlag_ShouldMarkAsSensitive()
    {
        // Arrange
        var envString = "API_KEY=secret-123";

        // Act
        var result = EnvironmentVariable.FromString(envString, isSensitive: true);

        // Assert
        result.Name.Should().Be("API_KEY");
        result.Value.Should().Be("secret-123");
        result.IsSensitive.Should().BeTrue();
    }

    [Fact]
    public void ToDockerString_ShouldReturnCorrectFormat()
    {
        // Arrange
        var envVar = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/mydb");

        // Act
        var result = envVar.ToDockerString();

        // Assert
        result.Should().Be("DATABASE_URL=postgresql://localhost:5432/mydb");
    }

    [Fact]
    public void GetDisplayValue_WithNonSensitive_ShouldReturnActualValue()
    {
        // Arrange
        var envVar = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/mydb");

        // Act
        var result = envVar.GetDisplayValue();

        // Assert
        result.Should().Be("postgresql://localhost:5432/mydb");
    }

    [Fact]
    public void GetDisplayValue_WithSensitive_ShouldReturnMaskedValue()
    {
        // Arrange
        var envVar = EnvironmentVariable.Create("API_KEY", "secret-123", isSensitive: true);

        // Act
        var result = envVar.GetDisplayValue();

        // Assert
        result.Should().Be("***");
    }

    [Fact]
    public void GetDisplayValue_WithSensitiveEmptyValue_ShouldReturnEmptyString()
    {
        // Arrange
        var envVar = EnvironmentVariable.Create("API_KEY", "", isSensitive: true);

        // Act
        var result = envVar.GetDisplayValue();

        // Assert
        result.Should().Be("");
    }

    [Fact]
    public void Equals_WithSameValues_ShouldReturnTrue()
    {
        // Arrange
        var envVar1 = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/mydb");
        var envVar2 = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/mydb");

        // Act & Assert
        envVar1.Should().Be(envVar2);
        envVar1.GetHashCode().Should().Be(envVar2.GetHashCode());
    }

    [Fact]
    public void Equals_WithDifferentValues_ShouldReturnFalse()
    {
        // Arrange
        var envVar1 = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/mydb");
        var envVar2 = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/otherdb");

        // Act & Assert
        envVar1.Should().NotBe(envVar2);
    }
}
