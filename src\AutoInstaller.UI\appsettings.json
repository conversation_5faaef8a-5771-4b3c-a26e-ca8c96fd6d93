{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Debug"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Docker.DotNet": "Information", "AutoInstaller": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/autoinstaller-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "Debug", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithEnvironmentName", "WithProcessId", "WithThreadId"]}, "ConnectionStrings": {"DefaultConnection": "Data Source=autoinstaller.db"}, "Docker": {"Endpoint": "npipe://./pipe/docker_engine", "Version": "1.41", "Timeout": 30, "EnableHealthChecks": true, "HealthCheckInterval": "00:00:30", "AutoDetectEngine": true, "FallbackEndpoints": ["tcp://localhost:2375", "tcp://localhost:2376"]}, "Application": {"Name": "Auto-Instalador Desktop", "Version": "1.0.0", "Environment": "Production", "EnablePlugins": true, "PluginDirectory": "plugins", "MaxConcurrentOperations": 5, "CacheExpirationMinutes": 30}, "UI": {"Theme": "FluentDark", "Language": "pt-BR", "EnableAnimations": true, "RefreshInterval": 5000, "PageSize": 20, "EnableNotifications": true}, "Plugins": {"EnableAutoLoad": true, "EnableHotReload": false, "IsolationLevel": "Process", "MaxPluginMemoryMB": 256, "PluginTimeout": 30, "EnabledPlugins": ["ContainerManagement", "Dashboard", "Settings"]}, "Security": {"EnableEncryption": true, "KeySize": 256, "EnableAuditLog": true, "SessionTimeoutMinutes": 60}, "Performance": {"EnableCaching": true, "CacheSize": 100, "EnableCompression": true, "MaxThreads": 10}}