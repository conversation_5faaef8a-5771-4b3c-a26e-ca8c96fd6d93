using ReactiveUI;
using AutoInstaller.Application.Queries;

namespace AutoInstaller.UI.ViewModels;

/// <summary>
/// View model for an image item in the list
/// </summary>
public sealed class ImageItemViewModel : ReactiveObject
{
    /// <summary>
    /// Image ID
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// Short image ID (first 12 characters)
    /// </summary>
    public string ShortId { get; }

    /// <summary>
    /// Image repository
    /// </summary>
    public string Repository { get; }

    /// <summary>
    /// Image tag
    /// </summary>
    public string Tag { get; }

    /// <summary>
    /// Full image name (repository:tag)
    /// </summary>
    public string FullName { get; }

    /// <summary>
    /// Image size in bytes
    /// </summary>
    public long Size { get; }

    /// <summary>
    /// Formatted size string
    /// </summary>
    public string FormattedSize { get; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; }

    /// <summary>
    /// Image description
    /// </summary>
    public string? Description { get; }

    /// <summary>
    /// Image author
    /// </summary>
    public string? Author { get; }

    /// <summary>
    /// All tags for this image
    /// </summary>
    public IReadOnlyList<string> Tags { get; }

    /// <summary>
    /// Whether this is a dangling image
    /// </summary>
    public bool IsDangling { get; }

    /// <summary>
    /// Whether this is an official image
    /// </summary>
    public bool IsOfficialImage { get; }

    /// <summary>
    /// Registry hostname (null for Docker Hub)
    /// </summary>
    public string? Registry { get; }

    /// <summary>
    /// Namespace/organization
    /// </summary>
    public string? Namespace { get; }

    /// <summary>
    /// Image name without registry and namespace
    /// </summary>
    public string ImageName { get; }

    /// <summary>
    /// Number of containers using this image
    /// </summary>
    public int ContainerCount { get; }

    /// <summary>
    /// Tags display text
    /// </summary>
    public string TagsText { get; }

    /// <summary>
    /// Status display text
    /// </summary>
    public string StatusText { get; }

    /// <summary>
    /// Status color for UI
    /// </summary>
    public string StatusColor { get; }

    /// <summary>
    /// Initialize image item view model
    /// </summary>
    /// <param name="image">Image DTO</param>
    public ImageItemViewModel(ImageDto image)
    {
        Id = image.Id;
        ShortId = image.ShortId;
        Repository = image.Repository;
        Tag = image.Tag;
        FullName = image.FullName;
        Size = image.Size;
        FormattedSize = image.FormattedSize;
        CreatedAt = image.CreatedAt;
        UpdatedAt = image.UpdatedAt;
        Description = image.Description;
        Author = image.Author;
        Tags = image.Tags;
        IsDangling = image.IsDangling;
        IsOfficialImage = image.IsOfficialImage;
        Registry = image.Registry;
        Namespace = image.Namespace;
        ImageName = image.ImageName;
        ContainerCount = image.ContainerCount;

        // Format tags
        if (Tags.Any())
        {
            TagsText = string.Join(", ", Tags);
        }
        else
        {
            TagsText = "Nenhuma";
        }

        // Status text and color
        if (IsDangling)
        {
            StatusText = "Órfã";
            StatusColor = "#E74C3C"; // Red
        }
        else if (ContainerCount > 0)
        {
            StatusText = $"Em uso ({ContainerCount} container{(ContainerCount > 1 ? "s" : "")})";
            StatusColor = "#00D084"; // Green
        }
        else
        {
            StatusText = "Disponível";
            StatusColor = "#3498DB"; // Blue
        }
    }

    /// <summary>
    /// Get formatted creation time
    /// </summary>
    /// <returns>Formatted creation time</returns>
    public string GetFormattedCreatedAt()
    {
        var timeSpan = DateTime.UtcNow - CreatedAt;
        
        return timeSpan.TotalDays switch
        {
            >= 1 => $"{(int)timeSpan.TotalDays} dia(s) atrás",
            _ when timeSpan.TotalHours >= 1 => $"{(int)timeSpan.TotalHours} hora(s) atrás",
            _ when timeSpan.TotalMinutes >= 1 => $"{(int)timeSpan.TotalMinutes} minuto(s) atrás",
            _ => "Agora mesmo"
        };
    }

    /// <summary>
    /// Get formatted update time
    /// </summary>
    /// <returns>Formatted update time</returns>
    public string GetFormattedUpdatedAt()
    {
        var timeSpan = DateTime.UtcNow - UpdatedAt;
        
        return timeSpan.TotalDays switch
        {
            >= 1 => $"{(int)timeSpan.TotalDays} dia(s) atrás",
            _ when timeSpan.TotalHours >= 1 => $"{(int)timeSpan.TotalHours} hora(s) atrás",
            _ when timeSpan.TotalMinutes >= 1 => $"{(int)timeSpan.TotalMinutes} minuto(s) atrás",
            _ => "Agora mesmo"
        };
    }

    /// <summary>
    /// Get registry display name
    /// </summary>
    /// <returns>Registry display name</returns>
    public string GetRegistryDisplayName()
    {
        return Registry switch
        {
            null => "Docker Hub",
            "docker.io" => "Docker Hub",
            "registry-1.docker.io" => "Docker Hub",
            "index.docker.io" => "Docker Hub",
            _ => Registry
        };
    }

    /// <summary>
    /// Get image type icon
    /// </summary>
    /// <returns>Image type icon</returns>
    public string GetImageTypeIcon()
    {
        if (IsDangling)
            return "🗑️";
        
        if (IsOfficialImage)
            return "⭐";
        
        if (ContainerCount > 0)
            return "📦";
        
        return "💿";
    }

    /// <summary>
    /// Get size category
    /// </summary>
    /// <returns>Size category</returns>
    public string GetSizeCategory()
    {
        const long MB = 1024 * 1024;
        const long GB = MB * 1024;

        return Size switch
        {
            < 10 * MB => "Muito Pequena",
            < 100 * MB => "Pequena",
            < 500 * MB => "Média",
            < GB => "Grande",
            _ => "Muito Grande"
        };
    }
}
