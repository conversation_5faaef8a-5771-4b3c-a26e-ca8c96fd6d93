<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>AutoInstaller.UI</RootNamespace>
    <AssemblyName>AutoInstaller.UI</AssemblyName>
    <Description>Presentation layer with Avalonia UI and plugin architecture for the Auto-Installer Desktop application.</Description>
    <StartupObject>AutoInstaller.UI.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoInstaller.Core\AutoInstaller.Core.csproj" />
    <ProjectReference Include="..\AutoInstaller.Application\AutoInstaller.Application.csproj" />
    <ProjectReference Include="..\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj" />
    <ProjectReference Include="..\AutoInstaller.Shared\AutoInstaller.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Avalonia UI -->
    <PackageReference Include="Avalonia" />
    <PackageReference Include="Avalonia.Desktop" />
    <PackageReference Include="Avalonia.Themes.Fluent" />
    <PackageReference Include="Avalonia.Fonts.Inter" />
    <PackageReference Include="Avalonia.ReactiveUI" />
    <PackageReference Include="Avalonia.Xaml.Behaviors" />
    <PackageReference Include="Avalonia.Controls.DataGrid" />
    <PackageReference Include="Avalonia.Svg.Skia" />
    <PackageReference Include="Avalonia.Diagnostics" Condition="'$(Configuration)' == 'Debug'" />
    
    <!-- ReactiveUI -->
    <PackageReference Include="ReactiveUI" />
    <PackageReference Include="ReactiveUI.Fody" />
    <PackageReference Include="ReactiveUI.Validation" />
    
    <!-- Plugin Architecture -->
    <PackageReference Include="System.Composition" />
    <PackageReference Include="System.Composition.AttributedModel" />
    <PackageReference Include="System.Composition.Convention" />
    <PackageReference Include="System.Composition.Hosting" />
    <PackageReference Include="System.Composition.Runtime" />
    <PackageReference Include="System.Composition.TypedParts" />
    <PackageReference Include="Microsoft.Extensions.DependencyModel" />
    
    <!-- Hosting and DI -->
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    
    <!-- Performance -->
    <PackageReference Include="System.Threading.Channels" />
    <PackageReference Include="System.Collections.Immutable" />
  </ItemGroup>

  <ItemGroup>
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Remove="appsettings.Development.json" />
    <Content Include="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
