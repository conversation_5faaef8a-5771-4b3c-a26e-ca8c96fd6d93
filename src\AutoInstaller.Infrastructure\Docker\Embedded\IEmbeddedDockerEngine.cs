using System;
using System.Threading;
using System.Threading.Tasks;

namespace AutoInstaller.Infrastructure.Docker.Embedded;

/// <summary>
/// Interface para gerenciamento de Docker Engine embarcado
/// Permite execução autônoma sem dependências externas de Docker
/// </summary>
public interface IEmbeddedDockerEngine : IDisposable
{
    /// <summary>
    /// Indica se a engine embarcada está executando
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// URI de conexão para Docker.DotNet
    /// </summary>
    string ConnectionUri { get; }

    /// <summary>
    /// Diretório de trabalho da engine embarcada
    /// </summary>
    string WorkingDirectory { get; }

    /// <summary>
    /// Versão da engine embarcada
    /// </summary>
    string EngineVersion { get; }

    /// <summary>
    /// Tipo de engine embarcada (Podman, Docker, etc.)
    /// </summary>
    string EngineType { get; }

    /// <summary>
    /// Inicializa e inicia a engine Docker embarcada
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Task representando a operação assíncrona</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Para a engine Docker embarcada
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Task representando a operação assíncrona</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Reinicia a engine Docker embarcada
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Task representando a operação assíncrona</returns>
    Task RestartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se a engine está saudável e responsiva
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se a engine está saudável</returns>
    Task<bool> HealthCheckAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações detalhadas sobre a engine
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações da engine</returns>
    Task<EmbeddedEngineInfo> GetEngineInfoAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Limpa recursos e dados temporários da engine
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Task representando a operação assíncrona</returns>
    Task CleanupAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Evento disparado quando o status da engine muda
    /// </summary>
    event EventHandler<EngineStatusChangedEventArgs> StatusChanged;

    /// <summary>
    /// Evento disparado quando ocorre um erro na engine
    /// </summary>
    event EventHandler<EngineErrorEventArgs> ErrorOccurred;
}

/// <summary>
/// Informações sobre a engine embarcada
/// </summary>
public class EmbeddedEngineInfo
{
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string Architecture { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
    public long MemoryUsage { get; set; }
    public int ContainerCount { get; set; }
    public int ImageCount { get; set; }
    public string WorkingDirectory { get; set; } = string.Empty;
    public string ConnectionUri { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// Argumentos do evento de mudança de status
/// </summary>
public class EngineStatusChangedEventArgs : EventArgs
{
    public EngineStatus PreviousStatus { get; }
    public EngineStatus CurrentStatus { get; }
    public DateTime Timestamp { get; }
    public string? Message { get; }

    public EngineStatusChangedEventArgs(EngineStatus previousStatus, EngineStatus currentStatus, string? message = null)
    {
        PreviousStatus = previousStatus;
        CurrentStatus = currentStatus;
        Timestamp = DateTime.UtcNow;
        Message = message;
    }
}

/// <summary>
/// Argumentos do evento de erro
/// </summary>
public class EngineErrorEventArgs : EventArgs
{
    public Exception Exception { get; }
    public string ErrorMessage { get; }
    public DateTime Timestamp { get; }
    public EngineErrorSeverity Severity { get; }

    public EngineErrorEventArgs(Exception exception, EngineErrorSeverity severity = EngineErrorSeverity.Error)
    {
        Exception = exception;
        ErrorMessage = exception.Message;
        Timestamp = DateTime.UtcNow;
        Severity = severity;
    }

    public EngineErrorEventArgs(string errorMessage, EngineErrorSeverity severity = EngineErrorSeverity.Error)
    {
        Exception = new InvalidOperationException(errorMessage);
        ErrorMessage = errorMessage;
        Timestamp = DateTime.UtcNow;
        Severity = severity;
    }
}

/// <summary>
/// Status da engine embarcada
/// </summary>
public enum EngineStatus
{
    Stopped,
    Starting,
    Running,
    Stopping,
    Error,
    Unknown
}

/// <summary>
/// Severidade do erro da engine
/// </summary>
public enum EngineErrorSeverity
{
    Info,
    Warning,
    Error,
    Critical
}
