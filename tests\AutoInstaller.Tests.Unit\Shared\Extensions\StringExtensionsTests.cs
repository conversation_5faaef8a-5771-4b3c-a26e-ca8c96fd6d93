using Xunit;
using FluentAssertions;

namespace AutoInstaller.Tests.Unit.Shared.Extensions;

/// <summary>
/// Unit tests for string extension methods
/// </summary>
public class StringExtensionsTests
{
    [Theory]
    [InlineData("", true)]
    [InlineData("   ", true)]
    [InlineData(null, true)]
    [InlineData("test", false)]
    [InlineData("  test  ", false)]
    public void IsNullOrWhiteSpace_ShouldReturnCorrectResult(string input, bool expected)
    {
        // Act
        var result = string.IsNullOrWhiteSpace(input);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("hello world", "Hello World")]
    [InlineData("HELLO WORLD", "Hello World")]
    [InlineData("hELLo WoRLd", "Hello World")]
    [InlineData("", "")]
    [InlineData("a", "A")]
    public void ToTitleCase_ShouldCapitalizeCorrectly(string input, string expected)
    {
        // Act
        var result = System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input.ToLower());

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("@domain.com", false)]
    [InlineData("user@", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public void IsValidEmail_ShouldValidateEmailFormat(string email, bool expected)
    {
        // Act
        var result = IsValidEmailFormat(email);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("Hello World", "Hello")]
    [InlineData("SingleWord", "SingleWord")]
    [InlineData("", "")]
    [InlineData("   ", "")]
    [InlineData("Word1 Word2 Word3", "Word1")]
    public void GetFirstWord_ShouldReturnFirstWord(string input, string expected)
    {
        // Act
        var result = GetFirstWord(input);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("container-name-123", true)]
    [InlineData("my_container", true)]
    [InlineData("container.name", true)]
    [InlineData("123container", true)]
    [InlineData("Container-Name", true)]
    [InlineData("", false)]
    [InlineData("   ", false)]
    [InlineData(null, false)]
    [InlineData("container name", false)] // spaces not allowed
    [InlineData("container@name", false)] // @ not allowed
    public void IsValidContainerName_ShouldValidateContainerName(string name, bool expected)
    {
        // Act
        var result = IsValidContainerName(name);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("1234567890abcdef", "1234567890abcdef")]
    [InlineData("1234567890ABCDEF", "1234567890abcdef")]
    [InlineData("MixedCaseHex123", "mixedcasehex123")]
    [InlineData("", "")]
    public void ToLowerInvariant_ShouldConvertToLowercase(string input, string expected)
    {
        // Act
        var result = input?.ToLowerInvariant() ?? "";

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void StringInterpolation_ShouldWorkCorrectly()
    {
        // Arrange
        var containerName = "nginx";
        var port = 8080;

        // Act
        var result = $"Container {containerName} is running on port {port}";

        // Assert
        result.Should().Be("Container nginx is running on port 8080");
    }

    // Helper methods for testing
    private static bool IsValidEmailFormat(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private static string GetFirstWord(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;

        var words = input.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        return words.Length > 0 ? words[0] : string.Empty;
    }

    private static bool IsValidContainerName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return false;

        // Basic validation: alphanumeric, hyphens, underscores, dots
        return System.Text.RegularExpressions.Regex.IsMatch(name, @"^[a-zA-Z0-9._-]+$");
    }
}
