using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Events;

namespace AutoInstaller.Core.Entities;

/// <summary>
/// Represents a Docker image entity in the domain
/// </summary>
public class Image
{
    private readonly List<DomainEvent> _domainEvents = new();
    private readonly List<string> _tags = new();

    /// <summary>
    /// Initialize a new image
    /// </summary>
    /// <param name="id">Image identifier</param>
    /// <param name="repository">Image repository</param>
    /// <param name="tag">Image tag</param>
    /// <param name="size">Image size in bytes</param>
    private Image(ImageId id, string repository, string tag, long size)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Repository = !string.IsNullOrWhiteSpace(repository) ? repository : throw new ArgumentException("Repository cannot be empty", nameof(repository));
        Tag = !string.IsNullOrWhiteSpace(tag) ? tag : throw new ArgumentException("Tag cannot be empty", nameof(tag));
        Size = size >= 0 ? size : throw new ArgumentException("Size cannot be negative", nameof(size));
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        
        _tags.Add(tag);
    }

    /// <summary>
    /// Image unique identifier
    /// </summary>
    public ImageId Id { get; private set; }

    /// <summary>
    /// Image repository name
    /// </summary>
    public string Repository { get; private set; }

    /// <summary>
    /// Primary image tag
    /// </summary>
    public string Tag { get; private set; }

    /// <summary>
    /// Image size in bytes
    /// </summary>
    public long Size { get; private set; }

    /// <summary>
    /// Image creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; private set; }

    /// <summary>
    /// Image description
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Image author/maintainer
    /// </summary>
    public string? Author { get; private set; }

    /// <summary>
    /// All tags associated with this image
    /// </summary>
    public IReadOnlyList<string> Tags => _tags.AsReadOnly();

    /// <summary>
    /// Full image name (repository:tag)
    /// </summary>
    public string FullName => $"{Repository}:{Tag}";

    /// <summary>
    /// Whether this image is marked as dangling (no tags)
    /// </summary>
    public bool IsDangling => !_tags.Any() || _tags.All(string.IsNullOrWhiteSpace);

    /// <summary>
    /// Domain events raised by this entity
    /// </summary>
    public IReadOnlyList<DomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Create a new image
    /// </summary>
    /// <param name="repository">Image repository</param>
    /// <param name="tag">Image tag</param>
    /// <param name="size">Image size in bytes</param>
    /// <param name="description">Optional description</param>
    /// <param name="author">Optional author</param>
    /// <returns>New image instance</returns>
    public static Image Create(string repository, string tag, long size, string? description = null, string? author = null)
    {
        var image = new Image(
            ImageId.New(),
            repository,
            tag,
            size
        );

        if (!string.IsNullOrWhiteSpace(description))
        {
            image.Description = description;
        }

        if (!string.IsNullOrWhiteSpace(author))
        {
            image.Author = author;
        }

        image.AddDomainEvent(new ImageCreatedEvent(image.Id, image.Repository, image.Tag, image.Size));
        return image;
    }

    /// <summary>
    /// Add a tag to this image
    /// </summary>
    /// <param name="tag">Tag to add</param>
    public void AddTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag))
            throw new ArgumentException("Tag cannot be empty", nameof(tag));

        if (_tags.Contains(tag))
            return; // Tag already exists

        _tags.Add(tag);
        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new ImageTagAddedEvent(Id, Repository, tag));
    }

    /// <summary>
    /// Remove a tag from this image
    /// </summary>
    /// <param name="tag">Tag to remove</param>
    public void RemoveTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag))
            return;

        if (_tags.Remove(tag))
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new ImageTagRemovedEvent(Id, Repository, tag));
        }
    }

    /// <summary>
    /// Update image metadata
    /// </summary>
    /// <param name="description">New description</param>
    /// <param name="author">New author</param>
    public void UpdateMetadata(string? description = null, string? author = null)
    {
        bool hasChanges = false;

        if (Description != description)
        {
            Description = description;
            hasChanges = true;
        }

        if (Author != author)
        {
            Author = author;
            hasChanges = true;
        }

        if (hasChanges)
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new ImageUpdatedEvent(Id, Repository, Tag));
        }
    }

    /// <summary>
    /// Update image size
    /// </summary>
    /// <param name="newSize">New size in bytes</param>
    public void UpdateSize(long newSize)
    {
        if (newSize < 0)
            throw new ArgumentException("Size cannot be negative", nameof(newSize));

        if (Size != newSize)
        {
            var oldSize = Size;
            Size = newSize;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new ImageSizeUpdatedEvent(Id, Repository, Tag, oldSize, newSize));
        }
    }

    /// <summary>
    /// Mark image for removal
    /// </summary>
    public void MarkForRemoval()
    {
        AddDomainEvent(new ImageMarkedForRemovalEvent(Id, Repository, Tag));
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Get formatted size string
    /// </summary>
    /// <returns>Human-readable size string</returns>
    public string GetFormattedSize()
    {
        const long KB = 1024;
        const long MB = KB * 1024;
        const long GB = MB * 1024;

        return Size switch
        {
            < KB => $"{Size} B",
            < MB => $"{Size / (double)KB:F1} KB",
            < GB => $"{Size / (double)MB:F1} MB",
            _ => $"{Size / (double)GB:F1} GB"
        };
    }

    /// <summary>
    /// Check if image has specific tag
    /// </summary>
    /// <param name="tag">Tag to check</param>
    /// <returns>True if image has the tag</returns>
    public bool HasTag(string tag)
    {
        return !string.IsNullOrWhiteSpace(tag) && _tags.Contains(tag);
    }

    /// <summary>
    /// Clear all domain events
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Add domain event
    /// </summary>
    /// <param name="domainEvent">Domain event to add</param>
    private void AddDomainEvent(DomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
}
