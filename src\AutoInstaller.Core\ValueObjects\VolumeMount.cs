namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker volume mount
/// </summary>
public sealed record VolumeMount
{
    /// <summary>
    /// Source path (host path or volume name)
    /// </summary>
    public string Source { get; }

    /// <summary>
    /// Target path inside the container
    /// </summary>
    public string ContainerPath { get; }

    /// <summary>
    /// Mount type (bind, volume, tmpfs)
    /// </summary>
    public MountType Type { get; }

    /// <summary>
    /// Whether the mount is read-only
    /// </summary>
    public bool IsReadOnly { get; }

    /// <summary>
    /// Initialize a new volume mount
    /// </summary>
    /// <param name="source">Source path or volume name</param>
    /// <param name="containerPath">Container path</param>
    /// <param name="type">Mount type</param>
    /// <param name="isReadOnly">Whether mount is read-only</param>
    private VolumeMount(string source, string containerPath, MountType type, bool isReadOnly)
    {
        Source = source;
        ContainerPath = containerPath;
        Type = type;
        IsReadOnly = isReadOnly;
    }

    /// <summary>
    /// Create a new volume mount
    /// </summary>
    /// <param name="source">Source path or volume name</param>
    /// <param name="containerPath">Container path</param>
    /// <param name="type">Mount type (default: Volume)</param>
    /// <param name="isReadOnly">Whether mount is read-only (default: false)</param>
    /// <returns>Volume mount instance</returns>
    /// <exception cref="ArgumentException">Thrown when paths are invalid</exception>
    public static VolumeMount Create(string source, string containerPath, MountType type = MountType.Volume, bool isReadOnly = false)
    {
        if (string.IsNullOrWhiteSpace(source))
            throw new ArgumentException("Source cannot be null or empty", nameof(source));

        if (string.IsNullOrWhiteSpace(containerPath))
            throw new ArgumentException("Container path cannot be null or empty", nameof(containerPath));

        ValidateContainerPath(containerPath);

        if (type == MountType.Bind)
        {
            ValidateHostPath(source);
        }

        return new VolumeMount(source, containerPath, type, isReadOnly);
    }

    /// <summary>
    /// Create volume mount from string format (e.g., "/host/path:/container/path:ro")
    /// </summary>
    /// <param name="mountString">Mount string</param>
    /// <returns>Volume mount instance</returns>
    /// <exception cref="ArgumentException">Thrown when format is invalid</exception>
    public static VolumeMount FromString(string mountString)
    {
        if (string.IsNullOrWhiteSpace(mountString))
            throw new ArgumentException("Mount string cannot be null or empty", nameof(mountString));

        var parts = mountString.Split(':');
        if (parts.Length < 2 || parts.Length > 3)
            throw new ArgumentException("Mount string must be in format 'source:target' or 'source:target:options'", nameof(mountString));

        var source = parts[0];
        var containerPath = parts[1];
        var isReadOnly = false;
        var type = MountType.Volume;

        // Parse options
        if (parts.Length == 3)
        {
            var options = parts[2].Split(',');
            isReadOnly = options.Contains("ro", StringComparer.OrdinalIgnoreCase);
        }

        // Determine mount type based on source
        if (IsAbsolutePath(source))
        {
            type = MountType.Bind;
        }
        else if (source == "tmpfs")
        {
            type = MountType.Tmpfs;
        }

        return Create(source, containerPath, type, isReadOnly);
    }

    /// <summary>
    /// Create a bind mount
    /// </summary>
    /// <param name="hostPath">Host path</param>
    /// <param name="containerPath">Container path</param>
    /// <param name="isReadOnly">Whether mount is read-only</param>
    /// <returns>Bind mount instance</returns>
    public static VolumeMount CreateBind(string hostPath, string containerPath, bool isReadOnly = false)
    {
        return Create(hostPath, containerPath, MountType.Bind, isReadOnly);
    }

    /// <summary>
    /// Create a volume mount
    /// </summary>
    /// <param name="volumeName">Volume name</param>
    /// <param name="containerPath">Container path</param>
    /// <param name="isReadOnly">Whether mount is read-only</param>
    /// <returns>Volume mount instance</returns>
    public static VolumeMount CreateVolume(string volumeName, string containerPath, bool isReadOnly = false)
    {
        return Create(volumeName, containerPath, MountType.Volume, isReadOnly);
    }

    /// <summary>
    /// Create a tmpfs mount
    /// </summary>
    /// <param name="containerPath">Container path</param>
    /// <param name="isReadOnly">Whether mount is read-only</param>
    /// <returns>Tmpfs mount instance</returns>
    public static VolumeMount CreateTmpfs(string containerPath, bool isReadOnly = false)
    {
        return Create("tmpfs", containerPath, MountType.Tmpfs, isReadOnly);
    }

    /// <summary>
    /// Check if this is a bind mount
    /// </summary>
    /// <returns>True if mount type is bind</returns>
    public bool IsBind()
    {
        return Type == MountType.Bind;
    }

    /// <summary>
    /// Check if this is a volume mount
    /// </summary>
    /// <returns>True if mount type is volume</returns>
    public bool IsVolume()
    {
        return Type == MountType.Volume;
    }

    /// <summary>
    /// Check if this is a tmpfs mount
    /// </summary>
    /// <returns>True if mount type is tmpfs</returns>
    public bool IsTmpfs()
    {
        return Type == MountType.Tmpfs;
    }

    /// <summary>
    /// Get string representation in Docker format
    /// </summary>
    /// <returns>Mount string</returns>
    public string ToDockerString()
    {
        var options = IsReadOnly ? ":ro" : "";
        return $"{Source}:{ContainerPath}{options}";
    }

    /// <summary>
    /// Validate container path
    /// </summary>
    /// <param name="path">Path to validate</param>
    /// <exception cref="ArgumentException">Thrown when path is invalid</exception>
    private static void ValidateContainerPath(string path)
    {
        if (!IsAbsolutePath(path))
            throw new ArgumentException("Container path must be absolute", nameof(path));
    }

    /// <summary>
    /// Validate host path for bind mounts
    /// </summary>
    /// <param name="path">Path to validate</param>
    /// <exception cref="ArgumentException">Thrown when path is invalid</exception>
    private static void ValidateHostPath(string path)
    {
        if (!IsAbsolutePath(path))
            throw new ArgumentException("Host path must be absolute for bind mounts", nameof(path));
    }

    /// <summary>
    /// Check if path is absolute
    /// </summary>
    /// <param name="path">Path to check</param>
    /// <returns>True if path is absolute</returns>
    private static bool IsAbsolutePath(string path)
    {
        return Path.IsPathRooted(path) || path.StartsWith('/');
    }

    /// <summary>
    /// String representation of volume mount
    /// </summary>
    /// <returns>Mount string</returns>
    public override string ToString() => ToDockerString();
}

/// <summary>
/// Mount type enumeration
/// </summary>
public enum MountType
{
    /// <summary>
    /// Bind mount (host directory)
    /// </summary>
    Bind,
    
    /// <summary>
    /// Volume mount (Docker volume)
    /// </summary>
    Volume,
    
    /// <summary>
    /// Tmpfs mount (temporary filesystem)
    /// </summary>
    Tmpfs
}
