using Avalonia.Controls;
using AutoInstaller.UI.ViewModels;

namespace AutoInstaller.UI.Views;

/// <summary>
/// Main window of the application
/// </summary>
public partial class MainWindow : Window
{
    /// <summary>
    /// Initialize the main window
    /// </summary>
    /// <param name="viewModel">Main window view model</param>
    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }

    /// <summary>
    /// Default constructor for design-time
    /// </summary>
    public MainWindow()
    {
        InitializeComponent();
    }
}
