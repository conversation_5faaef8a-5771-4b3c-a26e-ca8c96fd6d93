Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

# Core Projects (Domain Layer)
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Core", "src\AutoInstaller.Core\AutoInstaller.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject

# Application Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Application", "src\AutoInstaller.Application\AutoInstaller.Application.csproj", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject

# Infrastructure Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Infrastructure", "src\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj", "{C3D4E5F6-G7H8-9012-CDEF-G34567890123}"
EndProject

# Presentation Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.UI", "src\AutoInstaller.UI\AutoInstaller.UI.csproj", "{D4E5F6G7-H8I9-0123-DEFG-H45678901234}"
EndProject

# Shared Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Shared", "src\AutoInstaller.Shared\AutoInstaller.Shared.csproj", "{E5F6G7H8-I9J0-1234-EFGH-I56789012345}"
EndProject

# Test Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Tests.Unit", "tests\AutoInstaller.Tests.Unit\AutoInstaller.Tests.Unit.csproj", "{F6G7H8I9-J0K1-2345-FGHI-J67890123456}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Integration.Tests", "tests\AutoInstaller.Integration.Tests\AutoInstaller.Integration.Tests.csproj", "{G7H8I9J0-K1L2-3456-GHIJ-K78901234567}"
EndProject

# Solution Folders
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{11111111-**************-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{*************-4444-5555-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{33333333-4444-5555-6666-777777777777}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "scripts", "scripts", "{44444444-5555-6666-7777-888888888888}"
EndProject

# Solution Items
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{55555555-6666-7777-8888-999999999999}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		.gitignore = .gitignore
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		global.json = global.json
	EndProjectSection
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-G34567890123}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-G34567890123}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-G34567890123}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-G34567890123}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-H45678901234}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-H45678901234}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-H45678901234}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-H45678901234}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-I56789012345}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-I56789012345}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-I56789012345}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-I56789012345}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-J67890123456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-J67890123456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-J67890123456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-J67890123456}.Release|Any CPU.Build.0 = Release|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-K78901234567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-K78901234567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-K78901234567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-K78901234567}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {11111111-**************-************}
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012} = {11111111-**************-************}
		{C3D4E5F6-G7H8-9012-CDEF-G34567890123} = {11111111-**************-************}
		{D4E5F6G7-H8I9-0123-DEFG-H45678901234} = {11111111-**************-************}
		{E5F6G7H8-I9J0-1234-EFGH-I56789012345} = {11111111-**************-************}
		{F6G7H8I9-J0K1-2345-FGHI-J67890123456} = {*************-4444-5555-************}
		{G7H8I9J0-K1L2-3456-GHIJ-K78901234567} = {*************-4444-5555-************}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9012-123456789012}
	EndGlobalSection
EndGlobal
