<Project>
  
  <!-- Enable Central Package Management -->
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <!-- Core .NET Packages -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.0" />
  </ItemGroup>

  <!-- Avalonia UI Packages -->
  <ItemGroup>
    <PackageVersion Include="Avalonia" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Desktop" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Themes.Fluent" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Fonts.Inter" Version="11.3.4" />
    <PackageVersion Include="Avalonia.ReactiveUI" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Xaml.Behaviors" Version="11.3.0.6" />
    <PackageVersion Include="Avalonia.Controls.DataGrid" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Svg.Skia" Version="11.3.0" />
    <PackageVersion Include="Avalonia.Diagnostics" Version="11.3.4" />
  </ItemGroup>

  <!-- ReactiveUI Packages -->
  <ItemGroup>
    <PackageVersion Include="ReactiveUI" Version="20.1.63" />
    <PackageVersion Include="ReactiveUI.Fody" Version="19.5.41" />
    <PackageVersion Include="ReactiveUI.Validation" Version="4.0.9" />
  </ItemGroup>

  <!-- Docker Integration -->
  <ItemGroup>
    <PackageVersion Include="Docker.DotNet" Version="3.125.15" />
  </ItemGroup>

  <!-- CQRS and MediatR -->
  <ItemGroup>
    <PackageVersion Include="MediatR" Version="11.1.0" />
    <PackageVersion Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />
  </ItemGroup>

  <!-- Validation -->
  <ItemGroup>
    <PackageVersion Include="FluentValidation" Version="11.10.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.10.0" />
  </ItemGroup>

  <!-- Entity Framework Core -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
  </ItemGroup>

  <!-- Logging -->
  <ItemGroup>
    <PackageVersion Include="Serilog" Version="4.1.0" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageVersion Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageVersion Include="Serilog.Enrichers.Process" Version="3.0.0" />
    <PackageVersion Include="Serilog.Enrichers.Thread" Version="4.0.0" />
  </ItemGroup>

  <!-- Mapping -->
  <ItemGroup>
    <PackageVersion Include="AutoMapper" Version="12.0.1" />
    <PackageVersion Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
  </ItemGroup>

  <!-- JSON -->
  <ItemGroup>
    <PackageVersion Include="System.Text.Json" Version="9.0.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <!-- Health Checks -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="9.0.0" />
  </ItemGroup>

  <!-- Testing Packages -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageVersion Include="xunit" Version="2.9.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="xunit.runner.console" Version="2.9.2" />
    <PackageVersion Include="FluentAssertions" Version="6.12.2" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.AutoMoq" Version="4.18.1" />
    <PackageVersion Include="Testcontainers" Version="4.0.0" />
    <PackageVersion Include="Testcontainers.PostgreSql" Version="4.0.0" />
    <PackageVersion Include="BenchmarkDotNet" Version="0.14.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="coverlet.msbuild" Version="6.0.2" />
  </ItemGroup>

  <!-- Code Analysis -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.CodeAnalysis.Analyzers" Version="3.11.0" />
    <PackageVersion Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="9.0.0" />
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" />
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="9.35.0.90414" />
  </ItemGroup>

  <!-- Build Tools -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.Build.Tasks.Git" Version="8.0.0" />
    <PackageVersion Include="MinVer" Version="6.0.0" />
  </ItemGroup>

  <!-- Plugin Architecture -->
  <ItemGroup>
    <PackageVersion Include="System.Composition" Version="9.0.0" />
    <PackageVersion Include="System.Composition.AttributedModel" Version="9.0.0" />
    <PackageVersion Include="System.Composition.Convention" Version="9.0.0" />
    <PackageVersion Include="System.Composition.Hosting" Version="9.0.0" />
    <PackageVersion Include="System.Composition.Runtime" Version="9.0.0" />
    <PackageVersion Include="System.Composition.TypedParts" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyModel" Version="9.0.0" />
  </ItemGroup>

  <!-- Performance -->
  <ItemGroup>
    <PackageVersion Include="System.Threading.Channels" Version="9.0.0" />
    <PackageVersion Include="System.Collections.Immutable" Version="9.0.0" />
    <PackageVersion Include="System.Memory" Version="4.5.5" />
  </ItemGroup>

  <!-- Security -->
  <ItemGroup>
    <PackageVersion Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageVersion Include="System.Security.Cryptography.Primitives" Version="4.3.0" />
  </ItemGroup>

</Project>
