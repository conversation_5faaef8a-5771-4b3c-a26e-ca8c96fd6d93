<Project>
  
  <!-- Global Properties -->
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>NU1701;CS1591</WarningsNotAsErrors>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <!-- Assembly Information -->
  <PropertyGroup>
    <Product>Auto-Instalador Desktop</Product>
    <Company>DRS Developer</Company>
    <Authors>DRS Developer</Authors>
    <Copyright>Copyright © DRS Developer 2025</Copyright>
    <Description>Sistema multiplataforma para gerenciamento de containers Docker com interface moderna e intuitiva.</Description>
    <PackageProjectUrl>https://github.com/DRS-Developer/auto-instalador-desktop</PackageProjectUrl>
    <RepositoryUrl>https://github.com/DRS-Developer/auto-instalador-desktop</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageIcon>icon.png</PackageIcon>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageTags>docker;container;desktop;avalonia;cqrs;clean-architecture</PackageTags>
  </PropertyGroup>

  <!-- Version Information -->
  <PropertyGroup>
    <VersionPrefix>1.0.0</VersionPrefix>
    <VersionSuffix Condition="'$(Configuration)' == 'Debug'">dev</VersionSuffix>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <!-- Build Configuration -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>$(DefineConstants);RELEASE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
    <TrimMode>link</TrimMode>
    <PublishTrimmed>true</PublishTrimmed>
  </PropertyGroup>

  <!-- Source Link -->
  <PropertyGroup>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <!-- Code Analysis -->
  <PropertyGroup>
    <CodeAnalysisRuleSet>$(MSBuildThisFileDirectory)CodeAnalysis.ruleset</CodeAnalysisRuleSet>
    <RunAnalyzersDuringBuild>true</RunAnalyzersDuringBuild>
    <RunCodeAnalysis>true</RunCodeAnalysis>
  </PropertyGroup>

  <!-- Global Package References -->
  <ItemGroup>
    <PackageReference Include="Microsoft.SourceLink.GitHub" PrivateAssets="All" />
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" PrivateAssets="All" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" PrivateAssets="All" />
  </ItemGroup>

  <!-- Global Items -->
  <ItemGroup>
    <None Include="$(MSBuildThisFileDirectory)README.md" Pack="true" PackagePath="\" />
    <None Include="$(MSBuildThisFileDirectory)icon.png" Pack="true" PackagePath="\" Condition="Exists('$(MSBuildThisFileDirectory)icon.png')" />
  </ItemGroup>

  <!-- Avalonia UI Specific Properties -->
  <PropertyGroup Condition="$(MSBuildProjectName.Contains('UI'))">
    <OutputType>WinExe</OutputType>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    <UseAppHost>true</UseAppHost>
  </PropertyGroup>

  <!-- Test Project Properties -->
  <PropertyGroup Condition="$(MSBuildProjectName.Contains('Test'))">
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <!-- Docker Integration Properties -->
  <PropertyGroup>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>.</DockerfileContext>
  </PropertyGroup>

  <!-- Plugin Architecture Properties -->
  <PropertyGroup>
    <EnableDynamicLoading>true</EnableDynamicLoading>
    <PreserveCompilationContext>true</PreserveCompilationContext>
  </PropertyGroup>

</Project>
