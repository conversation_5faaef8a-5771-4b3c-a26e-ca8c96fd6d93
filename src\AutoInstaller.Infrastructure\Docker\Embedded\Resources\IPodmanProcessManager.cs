using System;
using System.Threading;
using System.Threading.Tasks;
using AutoInstaller.Infrastructure.Docker.Embedded;

namespace AutoInstaller.Infrastructure.Docker.Embedded.Resources;

/// <summary>
/// Interface para gerenciamento de processos Podman
/// </summary>
public interface IPodmanProcessManager : IDisposable
{
    /// <summary>
    /// Indica se o processo Podman está executando
    /// </summary>
    bool IsProcessRunning();

    /// <summary>
    /// Inicia o processo <PERSON>dman
    /// </summary>
    /// <param name="workingDirectory">Diretório de trabalho</param>
    /// <param name="socketUri">URI do socket para comunicação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Task representando a operação</returns>
    Task StartPodmanAsync(string workingDirectory, string socketUri, CancellationToken cancellationToken = default);

    /// <summary>
    /// Para o processo Podman
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Task representando a operação</returns>
    Task StopPodmanAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Testa conexão com Podman via socket
    /// </summary>
    /// <param name="socketUri">URI do socket</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se conexão bem-sucedida</returns>
    Task<bool> TestConnectionAsync(string socketUri, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações do processo Podman
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do processo</returns>
    Task<PodmanProcessInfo> GetProcessInfoAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Evento disparado quando status do processo muda
    /// </summary>
    event EventHandler<EngineStatusChangedEventArgs> StatusChanged;

    /// <summary>
    /// Evento disparado quando ocorre erro no processo
    /// </summary>
    event EventHandler<EngineErrorEventArgs> ErrorOccurred;
}

/// <summary>
/// Informações do processo Podman
/// </summary>
public class PodmanProcessInfo
{
    public int ProcessId { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
    public long MemoryUsage { get; set; }
    public double CpuUsage { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}
