using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Interfaces.Repositories;

/// <summary>
/// Repository interface for Container entity
/// </summary>
public interface IContainerRepository
{
    /// <summary>
    /// Get container by ID
    /// </summary>
    /// <param name="id">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Container if found, null otherwise</returns>
    Task<Container?> GetByIdAsync(ContainerId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get container by name
    /// </summary>
    /// <param name="name">Container name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Container if found, null otherwise</returns>
    Task<Container?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all containers
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all containers</returns>
    Task<IReadOnlyList<Container>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get containers by status
    /// </summary>
    /// <param name="status">Container status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of containers with specified status</returns>
    Task<IReadOnlyList<Container>> GetByStatusAsync(ContainerStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get containers by image tag
    /// </summary>
    /// <param name="imageTag">Image tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of containers using specified image</returns>
    Task<IReadOnlyList<Container>> GetByImageTagAsync(ImageTag imageTag, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get running containers
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of running containers</returns>
    Task<IReadOnlyList<Container>> GetRunningAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if container exists by ID
    /// </summary>
    /// <param name="id">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if container exists</returns>
    Task<bool> ExistsAsync(ContainerId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if container name is available
    /// </summary>
    /// <param name="name">Container name</param>
    /// <param name="excludeId">Container ID to exclude from check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if name is available</returns>
    Task<bool> IsNameAvailableAsync(string name, ContainerId? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add new container
    /// </summary>
    /// <param name="container">Container to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task AddAsync(Container container, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing container
    /// </summary>
    /// <param name="container">Container to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateAsync(Container container, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove container
    /// </summary>
    /// <param name="container">Container to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveAsync(Container container, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove container by ID
    /// </summary>
    /// <param name="id">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveByIdAsync(ContainerId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get containers with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of containers</returns>
    Task<(IReadOnlyList<Container> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search containers by name pattern
    /// </summary>
    /// <param name="namePattern">Name pattern to search</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching containers</returns>
    Task<IReadOnlyList<Container>> SearchByNameAsync(string namePattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get container count
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total number of containers</returns>
    Task<int> GetCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get container count by status
    /// </summary>
    /// <param name="status">Container status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of containers with specified status</returns>
    Task<int> GetCountByStatusAsync(ContainerStatus status, CancellationToken cancellationToken = default);
}
