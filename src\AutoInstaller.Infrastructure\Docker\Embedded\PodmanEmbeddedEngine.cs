using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AutoInstaller.Infrastructure.Docker.Embedded.Resources;

namespace AutoInstaller.Infrastructure.Docker.Embedded;

/// <summary>
/// Implementação de Docker Engine embarcado usando <PERSON>dman
/// Fornece runtime de containers autônomo sem dependências externas
/// </summary>
public class PodmanEmbeddedEngine : IEmbeddedDockerEngine
{
    private readonly ILogger<PodmanEmbeddedEngine> _logger;
    private readonly IPodmanBinaryExtractor _binaryExtractor;
    private readonly IPodmanProcessManager _processManager;
    private readonly IPodmanSocketManager _socketManager;
    
    private bool _disposed = false;
    private EngineStatus _status = EngineStatus.Stopped;
    private DateTime _startTime;
    private string _workingDirectory = string.Empty;
    private string _connectionUri = string.Empty;

    public PodmanEmbeddedEngine(
        ILogger<PodmanEmbeddedEngine> logger,
        IPodmanBinaryExtractor binaryExtractor,
        IPodmanProcessManager processManager,
        IPodmanSocketManager socketManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _binaryExtractor = binaryExtractor ?? throw new ArgumentNullException(nameof(binaryExtractor));
        _processManager = processManager ?? throw new ArgumentNullException(nameof(processManager));
        _socketManager = socketManager ?? throw new ArgumentNullException(nameof(socketManager));

        _processManager.StatusChanged += OnProcessStatusChanged;
        _processManager.ErrorOccurred += OnProcessError;
    }

    public bool IsRunning => _status == EngineStatus.Running;
    public string ConnectionUri => _connectionUri;
    public string WorkingDirectory => _workingDirectory;
    public string EngineVersion => "Podman 4.8.0"; // Versão embarcada
    public string EngineType => "Podman";

    public event EventHandler<EngineStatusChangedEventArgs>? StatusChanged;
    public event EventHandler<EngineErrorEventArgs>? ErrorOccurred;

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Iniciando Podman Engine embarcado...");
        
        try
        {
            SetStatus(EngineStatus.Starting);

            // 1. Extrair binários Podman para diretório temporário
            _workingDirectory = await _binaryExtractor.ExtractBinariesAsync(cancellationToken);
            _logger.LogDebug("Binários Podman extraídos para: {WorkingDirectory}", _workingDirectory);

            // 2. Configurar socket/pipe para comunicação
            _connectionUri = await _socketManager.SetupSocketAsync(_workingDirectory, cancellationToken);
            _logger.LogDebug("Socket Podman configurado: {ConnectionUri}", _connectionUri);

            // 3. Iniciar processo Podman
            await _processManager.StartPodmanAsync(_workingDirectory, _connectionUri, cancellationToken);
            _logger.LogDebug("Processo Podman iniciado");

            // 4. Aguardar disponibilidade do socket
            await _socketManager.WaitForSocketAvailabilityAsync(cancellationToken);
            _logger.LogDebug("Socket Podman disponível");

            // 5. Verificar saúde da engine
            var isHealthy = await HealthCheckAsync(cancellationToken);
            if (!isHealthy)
            {
                throw new InvalidOperationException("Podman Engine não passou no health check");
            }

            _startTime = DateTime.UtcNow;
            SetStatus(EngineStatus.Running);
            
            _logger.LogInformation("Podman Engine embarcado iniciado com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Falha ao iniciar Podman Engine embarcado");
            SetStatus(EngineStatus.Error);
            OnErrorOccurred(new EngineErrorEventArgs(ex, EngineErrorSeverity.Critical));
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Parando Podman Engine embarcado...");
        
        try
        {
            SetStatus(EngineStatus.Stopping);

            // 1. Parar processo Podman
            await _processManager.StopPodmanAsync(cancellationToken);
            _logger.LogDebug("Processo Podman parado");

            // 2. Limpar socket
            await _socketManager.CleanupSocketAsync(cancellationToken);
            _logger.LogDebug("Socket Podman limpo");

            // 3. Limpar diretório de trabalho
            await CleanupWorkingDirectoryAsync(cancellationToken);
            _logger.LogDebug("Diretório de trabalho limpo");

            SetStatus(EngineStatus.Stopped);
            _logger.LogInformation("Podman Engine embarcado parado com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Falha ao parar Podman Engine embarcado");
            SetStatus(EngineStatus.Error);
            OnErrorOccurred(new EngineErrorEventArgs(ex, EngineErrorSeverity.Error));
            throw;
        }
    }

    public async Task RestartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Reiniciando Podman Engine embarcado...");
        
        if (IsRunning)
        {
            await StopAsync(cancellationToken);
        }
        
        await StartAsync(cancellationToken);
    }

    public async Task<bool> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!IsRunning)
                return false;

            // Verificar se processo está executando
            if (!_processManager.IsProcessRunning())
                return false;

            // Verificar se socket está disponível
            if (!await _socketManager.IsSocketAvailableAsync(cancellationToken))
                return false;

            // Tentar comando básico via socket
            return await _processManager.TestConnectionAsync(_connectionUri, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Health check falhou");
            return false;
        }
    }

    public async Task<EmbeddedEngineInfo> GetEngineInfoAsync(CancellationToken cancellationToken = default)
    {
        var info = new EmbeddedEngineInfo
        {
            Version = EngineVersion,
            Type = EngineType,
            Platform = GetPlatformName(),
            Architecture = RuntimeInformation.ProcessArchitecture.ToString(),
            StartTime = _startTime,
            Uptime = DateTime.UtcNow - _startTime,
            WorkingDirectory = _workingDirectory,
            ConnectionUri = _connectionUri,
            IsHealthy = await HealthCheckAsync(cancellationToken)
        };

        if (IsRunning)
        {
            try
            {
                var processInfo = await _processManager.GetProcessInfoAsync(cancellationToken);
                info.MemoryUsage = processInfo.MemoryUsage;
                info.AdditionalInfo["ProcessId"] = processInfo.ProcessId;
                info.AdditionalInfo["CpuUsage"] = processInfo.CpuUsage;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Não foi possível obter informações do processo");
            }
        }

        return info;
    }

    public async Task CleanupAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Limpando recursos Podman Engine embarcado...");
        
        try
        {
            // Parar se estiver executando
            if (IsRunning)
            {
                await StopAsync(cancellationToken);
            }

            // Limpeza adicional de recursos
            await _binaryExtractor.CleanupExtractedBinariesAsync(cancellationToken);
            
            _logger.LogInformation("Limpeza concluída");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante limpeza");
            OnErrorOccurred(new EngineErrorEventArgs(ex, EngineErrorSeverity.Warning));
        }
    }

    private void SetStatus(EngineStatus newStatus)
    {
        var previousStatus = _status;
        _status = newStatus;
        
        _logger.LogDebug("Status mudou de {PreviousStatus} para {CurrentStatus}", previousStatus, newStatus);
        StatusChanged?.Invoke(this, new EngineStatusChangedEventArgs(previousStatus, newStatus));
    }

    private void OnProcessStatusChanged(object? sender, EngineStatusChangedEventArgs e)
    {
        SetStatus(e.CurrentStatus);
    }

    private void OnProcessError(object? sender, EngineErrorEventArgs e)
    {
        OnErrorOccurred(e);
    }

    private void OnErrorOccurred(EngineErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    private static string GetPlatformName()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            return "Windows";
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            return "Linux";
        if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            return "macOS";
        
        return "Unknown";
    }

    private async Task CleanupWorkingDirectoryAsync(CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(_workingDirectory) || !Directory.Exists(_workingDirectory))
            return;

        try
        {
            await Task.Run(() =>
            {
                Directory.Delete(_workingDirectory, recursive: true);
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Não foi possível limpar diretório de trabalho: {WorkingDirectory}", _workingDirectory);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            if (IsRunning)
            {
                StopAsync().GetAwaiter().GetResult();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao parar engine durante dispose");
        }

        _processManager?.Dispose();
        _socketManager?.Dispose();
        _binaryExtractor?.Dispose();

        _disposed = true;
    }
}
