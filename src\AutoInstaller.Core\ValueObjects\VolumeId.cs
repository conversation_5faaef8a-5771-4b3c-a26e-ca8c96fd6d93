namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker volume identifier
/// </summary>
public sealed record VolumeId
{
    /// <summary>
    /// Volume ID value
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Initialize a new volume ID
    /// </summary>
    /// <param name="value">Volume ID value</param>
    private VolumeId(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Create a new volume ID from string value
    /// </summary>
    /// <param name="value">Volume ID string</param>
    /// <returns>Volume ID instance</returns>
    /// <exception cref="ArgumentException">Thrown when value is invalid</exception>
    public static VolumeId From(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Volume ID cannot be null or empty", nameof(value));

        if (value.Length < 12)
            throw new ArgumentException("Volume ID must be at least 12 characters long", nameof(value));

        if (value.Length > 64)
            throw new ArgumentException("Volume ID cannot be longer than 64 characters", nameof(value));

        if (!IsValidHexString(value))
            throw new ArgumentException("Volume ID must contain only hexadecimal characters", nameof(value));

        return new VolumeId(value.ToLowerInvariant());
    }

    /// <summary>
    /// Generate a new unique volume ID
    /// </summary>
    /// <returns>New volume ID instance</returns>
    public static VolumeId New()
    {
        // Generate a 64-character hexadecimal string similar to Docker volume IDs
        var guid1 = Guid.NewGuid().ToString("N");
        var guid2 = Guid.NewGuid().ToString("N");
        var volumeIdValue = (guid1 + guid2)[..64];
        
        return new VolumeId(volumeIdValue);
    }

    /// <summary>
    /// Get short version of volume ID (first 12 characters)
    /// </summary>
    /// <returns>Short volume ID</returns>
    public string GetShortId()
    {
        return Value[..12];
    }

    /// <summary>
    /// Validate if string contains only hexadecimal characters
    /// </summary>
    /// <param name="value">String to validate</param>
    /// <returns>True if string is valid hexadecimal</returns>
    private static bool IsValidHexString(string value)
    {
        return value.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    /// <summary>
    /// Implicit conversion from string to VolumeId
    /// </summary>
    /// <param name="value">String value</param>
    public static implicit operator VolumeId(string value) => From(value);

    /// <summary>
    /// Implicit conversion from VolumeId to string
    /// </summary>
    /// <param name="volumeId">Volume ID</param>
    public static implicit operator string(VolumeId volumeId) => volumeId.Value;

    /// <summary>
    /// String representation of volume ID
    /// </summary>
    /// <returns>Volume ID value</returns>
    public override string ToString() => Value;
}
