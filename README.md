# 🐳 Auto-Instalador Desktop

**Sistema multiplataforma para gerenciamento de containers Docker com interface moderna e intuitiva.**

[![.NET](https://img.shields.io/badge/.NET-9.0-blue.svg)](https://dotnet.microsoft.com/)
[![Avalonia](https://img.shields.io/badge/Avalonia-11.3.4-purple.svg)](https://avaloniaui.net/)
[![Docker](https://img.shields.io/badge/Docker-Supported-blue.svg)](https://www.docker.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🚀 Características Principais

- **🎨 Interface Moderna**: UI responsiva com Avalonia e Material Design
- **🐳 Integração Docker**: Gerenciamento completo via Docker.DotNet
- **🏗️ Arquitetura Limpa**: Clean Architecture com CQRS
- **🧪 Testes Abrangentes**: Cobertura >80% com xUnit e Moq
- **🌐 Multiplataforma**: Windows, Linux e macOS
- **⚡ Performance**: Otimizado com .NET 9

## 🔧 Pré-requisitos

### **Docker Desktop (OBRIGATÓRIO)**
O Auto-Instalador Desktop requer Docker para funcionar completamente:

- **Windows**: [Docker Desktop for Windows](https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe)
- **macOS**: [Docker Desktop for Mac](https://desktop.docker.com/mac/main/amd64/Docker.dmg)
- **Linux**: [Docker Engine](https://docs.docker.com/engine/install/) ou Docker Desktop

### **Verificação Rápida**
```bash
# Verificar se Docker está instalado e funcionando
docker --version
docker info
docker run hello-world
```

### **Configuração Automática**
Execute o script de verificação após instalar Docker:
```powershell
# Windows
.\scripts\Verify-DockerEnvironment.ps1 -InstallImages -RunTests

# Linux/macOS
./scripts/verify-docker-environment.sh --install-images --run-tests
```

**⚠️ IMPORTANTE**: Sem Docker instalado, os integration tests falharão e funcionalidades principais não estarão disponíveis.

📚 **Guia completo**: [Docker Setup Guide](docs/Docker-Setup-Guide.md)

## 📋 Funcionalidades

### Gerenciamento de Containers
- ✅ Criar, iniciar, parar e remover containers
- ✅ Pausar e despausar containers
- ✅ Monitoramento de status em tempo real
- ✅ Visualização de logs
- ✅ Estatísticas de uso (CPU, memória, rede)

### Interface de Usuário
- ✅ Lista paginada com filtros avançados
- ✅ Busca textual em tempo real
- ✅ Ações contextuais por container
- ✅ Dashboard com estatísticas visuais
- ✅ Tema Material Design responsivo

### Arquitetura
- ✅ Clean Architecture com separação de camadas
- ✅ CQRS com MediatR
- ✅ Repository Pattern
- ✅ Dependency Injection
- ✅ Logging estruturado com Serilog

## 🛠️ Tecnologias

### Backend
- **.NET 9** - Framework principal
- **Docker.DotNet** - Integração nativa com Docker
- **Entity Framework Core** - ORM com SQLite
- **MediatR** - CQRS e mediação
- **FluentValidation** - Validações
- **AutoMapper** - Mapeamento de objetos
- **Serilog** - Logging estruturado

### Frontend
- **Avalonia 11.3.4** - UI multiplataforma
- **ReactiveUI** - MVVM reativo
- **Material Design** - Sistema de design
- **XAML** - Linguagem de marcação

### Testes
- **xUnit** - Framework de testes
- **FluentAssertions** - Assertions fluentes
- **Moq** - Framework de mocking
- **AutoFixture** - Geração de dados de teste
- **Coverlet** - Cobertura de código

## 🚀 Início Rápido

### Pré-requisitos
- .NET 9 SDK
- Docker Desktop
- Git

### Instalação

1. **Clone o repositório**
```bash
git clone https://github.com/DRS-Developer/auto-instalador-desktop.git
cd auto-instalador-desktop
```

2. **Restaure as dependências**
```bash
dotnet restore
```

3. **Execute a aplicação**
```bash
dotnet run --project src/AutoInstaller.UI
```

### Build Multiplataforma

**Windows:**
```powershell
.\build\build.ps1 -Target All -Configuration Release
```

**Linux/macOS:**
```bash
chmod +x build/build.sh
./build/build.sh All Release All
```

## 🧪 Testes

### Executar todos os testes
```bash
dotnet test --configuration Release
```

### Gerar relatório de cobertura
```bash
dotnet test --collect:"XPlat Code Coverage"
dotnet tool run reportgenerator --reports:"**/coverage.cobertura.xml" --targetdir:"coverage-report"
```

## 📁 Estrutura do Projeto

```
auto-instalador-desktop/
├── src/
│   ├── AutoInstaller.Core/           # Domain Layer
│   ├── AutoInstaller.Application/    # Application Layer (CQRS)
│   ├── AutoInstaller.Infrastructure/ # Infrastructure Layer
│   └── AutoInstaller.UI/            # Presentation Layer (Avalonia)
├── tests/
│   ├── AutoInstaller.Tests.Unit/    # Testes Unitários
│   └── AutoInstaller.Integration.Tests/ # Testes de Integração
├── build/
│   ├── build.ps1                    # Script de build Windows
│   └── build.sh                     # Script de build Linux/macOS
├── Documentos-Auto-Instalador/      # Documentação técnica
└── .github/workflows/               # CI/CD Pipeline
```

## 🏗️ Arquitetura

O projeto segue os princípios da **Clean Architecture** com separação clara de responsabilidades:

- **Core**: Entidades de domínio, value objects e interfaces
- **Application**: Casos de uso, commands, queries e handlers (CQRS)
- **Infrastructure**: Implementações concretas, repositórios e serviços externos
- **UI**: Interface de usuário com Avalonia e MVVM

## 📖 Documentação

A documentação completa está disponível na pasta `Documentos-Auto-Instalador/`:

- [Especificações Técnicas](Documentos-Auto-Instalador/01-ESPECIFICACOES_TECNICAS.md)
- [Arquitetura da UI](Documentos-Auto-Instalador/02-ARQUITETURA_UI.md)
- [Arquitetura Modular](Documentos-Auto-Instalador/03-ARQUITETURA_MODULAR.md)
- [Configurações de Ambiente](Documentos-Auto-Instalador/05-CONFIGURACOES_AMBIENTE.md)
- [Testes e Qualidade](Documentos-Auto-Instalador/07-TESTES_QUALIDADE.md)
- [Deployment e Distribuição](Documentos-Auto-Instalador/08-DEPLOYMENT_DISTRIBUICAO.md)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 👨‍💻 Autor

**DRS Developer**
- GitHub: [@DRS-Developer](https://github.com/DRS-Developer)
- Email: <EMAIL>

## 🙏 Agradecimentos

- [Avalonia UI](https://avaloniaui.net/) - Framework UI multiplataforma
- [Docker.DotNet](https://github.com/dotnet/Docker.DotNet) - Cliente .NET para Docker
- [MediatR](https://github.com/jbogard/MediatR) - Mediação e CQRS
- [FluentValidation](https://fluentvalidation.net/) - Validações fluentes
