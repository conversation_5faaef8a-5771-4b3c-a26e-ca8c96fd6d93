using MediatR;

namespace AutoInstaller.Application.Commands;

/// <summary>
/// Command to start a Docker container
/// </summary>
public sealed record StartContainerCommand : IRequest<ContainerOperationResult>
{
    /// <summary>
    /// Container ID or name
    /// </summary>
    public required string ContainerIdOrName { get; init; }
}

/// <summary>
/// Command to stop a Docker container
/// </summary>
public sealed record StopContainerCommand : IRequest<ContainerOperationResult>
{
    /// <summary>
    /// Container ID or name
    /// </summary>
    public required string ContainerIdOrName { get; init; }

    /// <summary>
    /// Timeout in seconds before force killing
    /// </summary>
    public int TimeoutSeconds { get; init; } = 10;
}

/// <summary>
/// Command to remove a Docker container
/// </summary>
public sealed record RemoveContainerCommand : IRequest<ContainerOperationResult>
{
    /// <summary>
    /// Container ID or name
    /// </summary>
    public required string ContainerIdOrName { get; init; }

    /// <summary>
    /// Force removal even if running
    /// </summary>
    public bool Force { get; init; } = false;

    /// <summary>
    /// Remove associated volumes
    /// </summary>
    public bool RemoveVolumes { get; init; } = false;
}

/// <summary>
/// Command to restart a Docker container
/// </summary>
public sealed record RestartContainerCommand : IRequest<ContainerOperationResult>
{
    /// <summary>
    /// Container ID or name
    /// </summary>
    public required string ContainerIdOrName { get; init; }

    /// <summary>
    /// Timeout in seconds before force killing
    /// </summary>
    public int TimeoutSeconds { get; init; } = 10;
}

/// <summary>
/// Command to pause a Docker container
/// </summary>
public sealed record PauseContainerCommand : IRequest<ContainerOperationResult>
{
    /// <summary>
    /// Container ID or name
    /// </summary>
    public required string ContainerIdOrName { get; init; }
}

/// <summary>
/// Command to unpause a Docker container
/// </summary>
public sealed record UnpauseContainerCommand : IRequest<ContainerOperationResult>
{
    /// <summary>
    /// Container ID or name
    /// </summary>
    public required string ContainerIdOrName { get; init; }
}

/// <summary>
/// Result of container operation
/// </summary>
public sealed record ContainerOperationResult
{
    /// <summary>
    /// Container ID
    /// </summary>
    public required string ContainerId { get; init; }

    /// <summary>
    /// Container name
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// Operation that was performed
    /// </summary>
    public required string Operation { get; init; }

    /// <summary>
    /// Current container status after operation
    /// </summary>
    public required string Status { get; init; }

    /// <summary>
    /// Success indicator
    /// </summary>
    public required bool Success { get; init; }

    /// <summary>
    /// Error message if operation failed
    /// </summary>
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Operation timestamp
    /// </summary>
    public required DateTime Timestamp { get; init; }
}
