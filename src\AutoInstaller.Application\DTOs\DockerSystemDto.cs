namespace AutoInstaller.Application.DTOs;

/// <summary>
/// Docker system information DTO
/// </summary>
public sealed record DockerSystemDto
{
    /// <summary>
    /// Docker version information
    /// </summary>
    public required DockerVersionDto Version { get; init; }

    /// <summary>
    /// Docker system information
    /// </summary>
    public required DockerSystemInfoDto SystemInfo { get; init; }

    /// <summary>
    /// Docker disk usage information
    /// </summary>
    public required DockerDiskUsageDto DiskUsage { get; init; }

    /// <summary>
    /// Whether Docker is available and running
    /// </summary>
    public required bool IsAvailable { get; init; }

    /// <summary>
    /// Last check timestamp
    /// </summary>
    public required DateTime LastChecked { get; init; }
}

/// <summary>
/// Docker version DTO
/// </summary>
public sealed record DockerVersionDto
{
    /// <summary>
    /// Docker version
    /// </summary>
    public required string Version { get; init; }

    /// <summary>
    /// API version
    /// </summary>
    public required string ApiVersion { get; init; }

    /// <summary>
    /// Git commit
    /// </summary>
    public required string GitCommit { get; init; }

    /// <summary>
    /// Go version
    /// </summary>
    public required string GoVersion { get; init; }

    /// <summary>
    /// Operating system
    /// </summary>
    public required string Os { get; init; }

    /// <summary>
    /// Architecture
    /// </summary>
    public required string Arch { get; init; }
}

/// <summary>
/// Docker system info DTO
/// </summary>
public sealed record DockerSystemInfoDto
{
    /// <summary>
    /// Number of running containers
    /// </summary>
    public required int ContainersRunning { get; init; }

    /// <summary>
    /// Number of paused containers
    /// </summary>
    public required int ContainersPaused { get; init; }

    /// <summary>
    /// Number of stopped containers
    /// </summary>
    public required int ContainersStopped { get; init; }

    /// <summary>
    /// Total number of containers
    /// </summary>
    public int ContainersTotal => ContainersRunning + ContainersPaused + ContainersStopped;

    /// <summary>
    /// Number of images
    /// </summary>
    public required int Images { get; init; }

    /// <summary>
    /// Server version
    /// </summary>
    public required string ServerVersion { get; init; }

    /// <summary>
    /// Storage driver
    /// </summary>
    public required string StorageDriver { get; init; }

    /// <summary>
    /// Total memory in bytes
    /// </summary>
    public required long TotalMemory { get; init; }

    /// <summary>
    /// Formatted total memory
    /// </summary>
    public string FormattedTotalMemory
    {
        get
        {
            const long KB = 1024;
            const long MB = KB * 1024;
            const long GB = MB * 1024;

            return TotalMemory switch
            {
                < KB => $"{TotalMemory} B",
                < MB => $"{TotalMemory / (double)KB:F1} KB",
                < GB => $"{TotalMemory / (double)MB:F1} MB",
                _ => $"{TotalMemory / (double)GB:F1} GB"
            };
        }
    }

    /// <summary>
    /// Number of CPUs
    /// </summary>
    public required int CpuCount { get; init; }
}

/// <summary>
/// Docker disk usage DTO
/// </summary>
public sealed record DockerDiskUsageDto
{
    /// <summary>
    /// Total size of images in bytes
    /// </summary>
    public required long ImagesSize { get; init; }

    /// <summary>
    /// Total size of containers in bytes
    /// </summary>
    public required long ContainersSize { get; init; }

    /// <summary>
    /// Total size of volumes in bytes
    /// </summary>
    public required long VolumesSize { get; init; }

    /// <summary>
    /// Total size of build cache in bytes
    /// </summary>
    public required long BuildCacheSize { get; init; }

    /// <summary>
    /// Total size of all Docker resources
    /// </summary>
    public long TotalSize => ImagesSize + ContainersSize + VolumesSize + BuildCacheSize;

    /// <summary>
    /// Formatted images size
    /// </summary>
    public string FormattedImagesSize => FormatBytes(ImagesSize);

    /// <summary>
    /// Formatted containers size
    /// </summary>
    public string FormattedContainersSize => FormatBytes(ContainersSize);

    /// <summary>
    /// Formatted volumes size
    /// </summary>
    public string FormattedVolumesSize => FormatBytes(VolumesSize);

    /// <summary>
    /// Formatted build cache size
    /// </summary>
    public string FormattedBuildCacheSize => FormatBytes(BuildCacheSize);

    /// <summary>
    /// Formatted total size
    /// </summary>
    public string FormattedTotalSize => FormatBytes(TotalSize);

    /// <summary>
    /// Format bytes to human-readable string
    /// </summary>
    /// <param name="bytes">Bytes to format</param>
    /// <returns>Formatted string</returns>
    private static string FormatBytes(long bytes)
    {
        const long KB = 1024;
        const long MB = KB * 1024;
        const long GB = MB * 1024;

        return bytes switch
        {
            < KB => $"{bytes} B",
            < MB => $"{bytes / (double)KB:F1} KB",
            < GB => $"{bytes / (double)MB:F1} MB",
            _ => $"{bytes / (double)GB:F1} GB"
        };
    }
}

/// <summary>
/// Docker cleanup result DTO
/// </summary>
public sealed record DockerCleanupResultDto
{
    /// <summary>
    /// Number of images deleted
    /// </summary>
    public required int ImagesDeleted { get; init; }

    /// <summary>
    /// Number of containers deleted
    /// </summary>
    public required int ContainersDeleted { get; init; }

    /// <summary>
    /// Number of volumes deleted
    /// </summary>
    public required int VolumesDeleted { get; init; }

    /// <summary>
    /// Number of networks deleted
    /// </summary>
    public required int NetworksDeleted { get; init; }

    /// <summary>
    /// Space reclaimed in bytes
    /// </summary>
    public required long SpaceReclaimed { get; init; }

    /// <summary>
    /// Formatted space reclaimed
    /// </summary>
    public string FormattedSpaceReclaimed
    {
        get
        {
            const long KB = 1024;
            const long MB = KB * 1024;
            const long GB = MB * 1024;

            return SpaceReclaimed switch
            {
                < KB => $"{SpaceReclaimed} B",
                < MB => $"{SpaceReclaimed / (double)KB:F1} KB",
                < GB => $"{SpaceReclaimed / (double)MB:F1} MB",
                _ => $"{SpaceReclaimed / (double)GB:F1} GB"
            };
        }
    }

    /// <summary>
    /// Total items deleted
    /// </summary>
    public int TotalItemsDeleted => ImagesDeleted + ContainersDeleted + VolumesDeleted + NetworksDeleted;

    /// <summary>
    /// Success indicator
    /// </summary>
    public required bool Success { get; init; }

    /// <summary>
    /// Error message if cleanup failed
    /// </summary>
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Cleanup timestamp
    /// </summary>
    public required DateTime Timestamp { get; init; }
}
