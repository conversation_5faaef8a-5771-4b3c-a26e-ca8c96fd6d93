using Xunit;
using FluentAssertions;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Tests.Unit.Core.Entities;

/// <summary>
/// Unit tests for Container entity
/// </summary>
public class ContainerTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldReturnContainer()
    {
        // Arrange
        var id = ContainerId.New();
        var name = "test-container";
        var imageTag = ImageTag.From("nginx:latest");
        var description = "Test container description";

        // Act
        var container = Container.Create(id, name, imageTag, description);

        // Assert
        container.Should().NotBeNull();
        container.Id.Should().Be(id);
        container.Name.Should().Be(name);
        container.ImageTag.Should().Be(imageTag);
        container.Description.Should().Be(description);
        container.Status.Should().Be(ContainerStatus.Created);
        container.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Create_WithoutDescription_ShouldCreateContainerWithNullDescription()
    {
        // Arrange
        var id = ContainerId.New();
        var name = "test-container";
        var imageTag = ImageTag.From("nginx:latest");

        // Act
        var container = Container.Create(id, name, imageTag);

        // Assert
        container.Description.Should().BeNull();
    }

    [Fact]
    public void Start_WhenCreated_ShouldChangeStatusToRunning()
    {
        // Arrange
        var container = CreateTestContainer();
        var originalUpdatedAt = container.UpdatedAt;

        // Act
        container.Start();

        // Assert
        container.Status.Should().Be(ContainerStatus.Running);
        container.UpdatedAt.Should().BeAfter(originalUpdatedAt);
    }

    [Fact]
    public void Start_WhenAlreadyRunning_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var container = CreateTestContainer();
        container.Start(); // First start

        // Act & Assert
        var action = () => container.Start();
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Container is already running");
    }

    [Fact]
    public void Stop_WhenRunning_ShouldChangeStatusToStopped()
    {
        // Arrange
        var container = CreateTestContainer();
        container.Start();
        var originalUpdatedAt = container.UpdatedAt;

        // Act
        container.Stop();

        // Assert
        container.Status.Should().Be(ContainerStatus.Stopped);
        container.UpdatedAt.Should().BeAfter(originalUpdatedAt);
    }

    [Fact]
    public void Stop_WhenNotRunning_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var container = CreateTestContainer();

        // Act & Assert
        var action = () => container.Stop();
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Container is not running");
    }

    [Fact]
    public void Remove_WhenStopped_ShouldChangeStatusToRemoved()
    {
        // Arrange
        var container = CreateTestContainer();
        container.Start();
        container.Stop();
        var originalUpdatedAt = container.UpdatedAt;

        // Act
        container.Remove();

        // Assert
        container.Status.Should().Be(ContainerStatus.Removed);
        container.UpdatedAt.Should().BeAfter(originalUpdatedAt);
    }

    [Fact]
    public void Remove_WhenRunning_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var container = CreateTestContainer();
        container.Start();

        // Act & Assert
        var action = () => container.Remove();
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot remove a running container");
    }

    [Fact]
    public void AddEnvironmentVariable_WithValidVariable_ShouldAddToCollection()
    {
        // Arrange
        var container = CreateTestContainer();
        var envVar = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/mydb");

        // Act
        container.AddEnvironmentVariable(envVar);

        // Assert
        container.EnvironmentVariables.Should().Contain(envVar);
        container.EnvironmentVariables.Should().HaveCount(1);
    }

    private static Container CreateTestContainer()
    {
        var id = ContainerId.New();
        var name = "test-container";
        var imageTag = ImageTag.From("nginx:latest");
        return Container.Create(id, name, imageTag);
    }
}
