using System;
using System.Threading;
using System.Threading.Tasks;

namespace AutoInstaller.Infrastructure.Docker.Embedded.Resources;

/// <summary>
/// Interface para extração de binários Podman embarcados
/// </summary>
public interface IPodmanBinaryExtractor : IDisposable
{
    /// <summary>
    /// Extrai binários Podman para diretório temporário
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Caminho do diretório onde os binários foram extraídos</returns>
    Task<string> ExtractBinariesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se os binários já foram extraídos
    /// </summary>
    /// <param name="extractPath">Caminho de extração</param>
    /// <returns>True se os binários existem e são válidos</returns>
    Task<bool> AreBinariesExtractedAsync(string extractPath);

    /// <summary>
    /// Limpa binários extraídos
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Task representando a operação</returns>
    Task CleanupExtractedBinariesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações sobre os binários embarcados
    /// </summary>
    /// <returns>Informações dos binários</returns>
    Task<PodmanBinaryInfo> GetBinaryInfoAsync();
}

/// <summary>
/// Informações sobre binários Podman embarcados
/// </summary>
public class PodmanBinaryInfo
{
    public string Version { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string Architecture { get; set; } = string.Empty;
    public long TotalSize { get; set; }
    public DateTime BuildDate { get; set; }
    public string[] RequiredFiles { get; set; } = Array.Empty<string>();
    public Dictionary<string, string> FileHashes { get; set; } = new();
}
