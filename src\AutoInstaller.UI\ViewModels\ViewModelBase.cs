using ReactiveUI;

namespace AutoInstaller.UI.ViewModels;

/// <summary>
/// Base class for all view models using ReactiveUI
/// </summary>
public abstract class ViewModelBase : ReactiveObject
{
    /// <summary>
    /// Indicates if the view model is currently busy
    /// </summary>
    private bool _isBusy;
    public bool IsBusy
    {
        get => _isBusy;
        set => this.RaiseAndSetIfChanged(ref _isBusy, value);
    }

    /// <summary>
    /// Error message to display to the user
    /// </summary>
    private string? _errorMessage;
    public string? ErrorMessage
    {
        get => _errorMessage;
        set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
    }

    /// <summary>
    /// Success message to display to the user
    /// </summary>
    private string? _successMessage;
    public string? SuccessMessage
    {
        get => _successMessage;
        set => this.RaiseAndSetIfChanged(ref _successMessage, value);
    }

    /// <summary>
    /// Title of the view model
    /// </summary>
    private string _title = string.Empty;
    public string Title
    {
        get => _title;
        set => this.RaiseAndSetIfChanged(ref _title, value);
    }

    /// <summary>
    /// Clear all messages
    /// </summary>
    protected void ClearMessages()
    {
        ErrorMessage = null;
        SuccessMessage = null;
    }

    /// <summary>
    /// Set error message and clear success message
    /// </summary>
    /// <param name="message">Error message</param>
    protected void SetError(string message)
    {
        ErrorMessage = message;
        SuccessMessage = null;
    }

    /// <summary>
    /// Set success message and clear error message
    /// </summary>
    /// <param name="message">Success message</param>
    protected void SetSuccess(string message)
    {
        SuccessMessage = message;
        ErrorMessage = null;
    }

    /// <summary>
    /// Execute an async operation with busy state management
    /// </summary>
    /// <param name="operation">Operation to execute</param>
    /// <param name="clearMessages">Whether to clear messages before execution</param>
    protected async Task ExecuteWithBusyStateAsync(Func<Task> operation, bool clearMessages = true)
    {
        if (IsBusy) return;

        try
        {
            IsBusy = true;
            if (clearMessages) ClearMessages();

            await operation();
        }
        catch (Exception ex)
        {
            SetError(ex.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// Execute an async operation with busy state management and return result
    /// </summary>
    /// <typeparam name="T">Result type</typeparam>
    /// <param name="operation">Operation to execute</param>
    /// <param name="clearMessages">Whether to clear messages before execution</param>
    /// <returns>Operation result or default value on error</returns>
    protected async Task<T?> ExecuteWithBusyStateAsync<T>(Func<Task<T>> operation, bool clearMessages = true)
    {
        if (IsBusy) return default;

        try
        {
            IsBusy = true;
            if (clearMessages) ClearMessages();

            return await operation();
        }
        catch (Exception ex)
        {
            SetError(ex.Message);
            return default;
        }
        finally
        {
            IsBusy = false;
        }
    }
}
