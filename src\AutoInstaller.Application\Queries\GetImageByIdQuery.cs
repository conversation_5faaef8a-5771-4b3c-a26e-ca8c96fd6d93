using MediatR;

namespace AutoInstaller.Application.Queries;

/// <summary>
/// Query to get an image by ID
/// </summary>
public sealed record GetImageByIdQuery : IRequest<ImageDto?>
{
    /// <summary>
    /// Image ID
    /// </summary>
    public required string ImageId { get; init; }
}

/// <summary>
/// Query to get an image by repository and tag
/// </summary>
public sealed record GetImageByTagQuery : IRequest<ImageDto?>
{
    /// <summary>
    /// Image repository
    /// </summary>
    public required string Repository { get; init; }

    /// <summary>
    /// Image tag
    /// </summary>
    public required string Tag { get; init; }
}

/// <summary>
/// Query to get all images
/// </summary>
public sealed record GetAllImagesQuery : IRequest<IReadOnlyList<ImageDto>>
{
    /// <summary>
    /// Include dangling images
    /// </summary>
    public bool IncludeDangling { get; init; } = true;
}

/// <summary>
/// Query to get images with pagination
/// </summary>
public sealed record GetImagesPagedQuery : IRequest<PagedResult<ImageDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; init; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; init; } = 20;

    /// <summary>
    /// Search by repository pattern
    /// </summary>
    public string? RepositoryPattern { get; init; }

    /// <summary>
    /// Filter by tag
    /// </summary>
    public string? TagFilter { get; init; }

    /// <summary>
    /// Include dangling images
    /// </summary>
    public bool IncludeDangling { get; init; } = true;
}

/// <summary>
/// Query to get dangling images
/// </summary>
public sealed record GetDanglingImagesQuery : IRequest<IReadOnlyList<ImageDto>>
{
}

/// <summary>
/// Query to get images by repository
/// </summary>
public sealed record GetImagesByRepositoryQuery : IRequest<IReadOnlyList<ImageDto>>
{
    /// <summary>
    /// Repository name
    /// </summary>
    public required string Repository { get; init; }
}

/// <summary>
/// Query to get image disk usage
/// </summary>
public sealed record GetImageDiskUsageQuery : IRequest<ImageDiskUsageDto>
{
}

/// <summary>
/// Image DTO
/// </summary>
public sealed record ImageDto
{
    /// <summary>
    /// Image ID
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// Short image ID (first 12 characters)
    /// </summary>
    public required string ShortId { get; init; }

    /// <summary>
    /// Image repository
    /// </summary>
    public required string Repository { get; init; }

    /// <summary>
    /// Image tag
    /// </summary>
    public required string Tag { get; init; }

    /// <summary>
    /// Full image name (repository:tag)
    /// </summary>
    public required string FullName { get; init; }

    /// <summary>
    /// Image size in bytes
    /// </summary>
    public required long Size { get; init; }

    /// <summary>
    /// Formatted size string
    /// </summary>
    public required string FormattedSize { get; init; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public required DateTime CreatedAt { get; init; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public required DateTime UpdatedAt { get; init; }

    /// <summary>
    /// Image description
    /// </summary>
    public string? Description { get; init; }

    /// <summary>
    /// Image author
    /// </summary>
    public string? Author { get; init; }

    /// <summary>
    /// All tags for this image
    /// </summary>
    public IReadOnlyList<string> Tags { get; init; } = Array.Empty<string>();

    /// <summary>
    /// Whether this is a dangling image
    /// </summary>
    public required bool IsDangling { get; init; }

    /// <summary>
    /// Whether this is an official image
    /// </summary>
    public required bool IsOfficialImage { get; init; }

    /// <summary>
    /// Registry hostname (null for Docker Hub)
    /// </summary>
    public string? Registry { get; init; }

    /// <summary>
    /// Namespace/organization
    /// </summary>
    public string? Namespace { get; init; }

    /// <summary>
    /// Image name without registry and namespace
    /// </summary>
    public required string ImageName { get; init; }

    /// <summary>
    /// Number of containers using this image
    /// </summary>
    public required int ContainerCount { get; init; }
}

/// <summary>
/// Image disk usage DTO
/// </summary>
public sealed record ImageDiskUsageDto
{
    /// <summary>
    /// Total number of images
    /// </summary>
    public required int TotalImages { get; init; }

    /// <summary>
    /// Number of dangling images
    /// </summary>
    public required int DanglingImages { get; init; }

    /// <summary>
    /// Total size of all images in bytes
    /// </summary>
    public required long TotalSize { get; init; }

    /// <summary>
    /// Size of dangling images in bytes
    /// </summary>
    public required long DanglingSize { get; init; }

    /// <summary>
    /// Formatted total size string
    /// </summary>
    public string FormattedTotalSize
    {
        get
        {
            const long KB = 1024;
            const long MB = KB * 1024;
            const long GB = MB * 1024;

            return TotalSize switch
            {
                < KB => $"{TotalSize} B",
                < MB => $"{TotalSize / (double)KB:F1} KB",
                < GB => $"{TotalSize / (double)MB:F1} MB",
                _ => $"{TotalSize / (double)GB:F1} GB"
            };
        }
    }

    /// <summary>
    /// Formatted dangling size string
    /// </summary>
    public string FormattedDanglingSize
    {
        get
        {
            const long KB = 1024;
            const long MB = KB * 1024;
            const long GB = MB * 1024;

            return DanglingSize switch
            {
                < KB => $"{DanglingSize} B",
                < MB => $"{DanglingSize / (double)KB:F1} KB",
                < GB => $"{DanglingSize / (double)MB:F1} MB",
                _ => $"{DanglingSize / (double)GB:F1} GB"
            };
        }
    }

    /// <summary>
    /// Percentage of dangling images
    /// </summary>
    public double DanglingPercentage => TotalImages > 0 ? (double)DanglingImages / TotalImages * 100 : 0;

    /// <summary>
    /// Percentage of dangling size
    /// </summary>
    public double DanglingSizePercentage => TotalSize > 0 ? (double)DanglingSize / TotalSize * 100 : 0;
}
