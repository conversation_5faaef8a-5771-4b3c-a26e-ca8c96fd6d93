using MediatR;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Application.Commands;

/// <summary>
/// Command to create a new Docker container
/// </summary>
public sealed record CreateContainerCommand : IRequest<CreateContainerResult>
{
    /// <summary>
    /// Container name
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// Docker image tag
    /// </summary>
    public required string ImageTag { get; init; }

    /// <summary>
    /// Container description
    /// </summary>
    public string? Description { get; init; }

    /// <summary>
    /// Port mappings
    /// </summary>
    public IReadOnlyList<CreatePortMappingDto> PortMappings { get; init; } = Array.Empty<CreatePortMappingDto>();

    /// <summary>
    /// Volume mounts
    /// </summary>
    public IReadOnlyList<CreateVolumeMountDto> VolumeMounts { get; init; } = Array.Empty<CreateVolumeMountDto>();

    /// <summary>
    /// Environment variables
    /// </summary>
    public IReadOnlyList<CreateEnvironmentVariableDto> EnvironmentVariables { get; init; } = Array.Empty<CreateEnvironmentVariableDto>();

    /// <summary>
    /// Networks to connect to
    /// </summary>
    public IReadOnlyList<string> Networks { get; init; } = Array.Empty<string>();

    /// <summary>
    /// Whether to start the container immediately after creation
    /// </summary>
    public bool StartImmediately { get; init; } = false;
}

/// <summary>
/// Port mapping DTO for container creation
/// </summary>
public sealed record CreatePortMappingDto
{
    /// <summary>
    /// Host port
    /// </summary>
    public required int HostPort { get; init; }

    /// <summary>
    /// Container port
    /// </summary>
    public required int ContainerPort { get; init; }

    /// <summary>
    /// Protocol (TCP or UDP)
    /// </summary>
    public string Protocol { get; init; } = "TCP";

    /// <summary>
    /// Host IP address (optional)
    /// </summary>
    public string? HostIP { get; init; }
}

/// <summary>
/// Volume mount DTO for container creation
/// </summary>
public sealed record CreateVolumeMountDto
{
    /// <summary>
    /// Source path or volume name
    /// </summary>
    public required string Source { get; init; }

    /// <summary>
    /// Container path
    /// </summary>
    public required string ContainerPath { get; init; }

    /// <summary>
    /// Mount type (bind, volume, tmpfs)
    /// </summary>
    public string Type { get; init; } = "volume";

    /// <summary>
    /// Whether mount is read-only
    /// </summary>
    public bool IsReadOnly { get; init; } = false;
}

/// <summary>
/// Environment variable DTO for container creation
/// </summary>
public sealed record CreateEnvironmentVariableDto
{
    /// <summary>
    /// Variable name
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// Variable value
    /// </summary>
    public required string Value { get; init; }

    /// <summary>
    /// Whether variable is sensitive
    /// </summary>
    public bool? IsSensitive { get; init; }
}

/// <summary>
/// Result of container creation
/// </summary>
public sealed record CreateContainerResult
{
    /// <summary>
    /// Container ID
    /// </summary>
    public required string ContainerId { get; init; }

    /// <summary>
    /// Container name
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// Image tag used
    /// </summary>
    public required string ImageTag { get; init; }

    /// <summary>
    /// Current container status
    /// </summary>
    public required string Status { get; init; }

    /// <summary>
    /// Whether container was started
    /// </summary>
    public required bool IsStarted { get; init; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public required DateTime CreatedAt { get; init; }

    /// <summary>
    /// Success indicator
    /// </summary>
    public required bool Success { get; init; }

    /// <summary>
    /// Error message if creation failed
    /// </summary>
    public string? ErrorMessage { get; init; }
}
