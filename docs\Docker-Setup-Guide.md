# Guia de Configuração Docker - Auto-Instalador Desktop

Este documento fornece instruções completas para configurar o Docker no ambiente de desenvolvimento do **Auto-Instalador Desktop**, garantindo que todos os integration tests executem com sucesso.

## 📋 **Requisitos do Sistema**

### **Windows (Recomendado)**
- Windows 10/11 Pro, Enterprise ou Education (64-bit)
- WSL 2 habilitado
- Hyper-V habilitado (ou Docker Desktop com WSL 2 backend)
- Mínimo 4GB RAM (8GB recomendado)
- Mínimo 20GB espaço livre em disco

### **Alternativa Windows Home**
- Windows 10/11 Home (64-bit) com WSL 2
- Docker Desktop com WSL 2 backend

---

## 🚀 **Instalação Docker Desktop (Windows)**

### **Passo 1: Habilitar WSL 2**
```powershell
# Execute como Administrador
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

# Reinicie o sistema
```

### **Passo 2: Instalar WSL 2 Kernel Update**
1. Baixe o [WSL2 Linux kernel update package](https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi)
2. Execute o instalador
3. Configure WSL 2 como padrão:
```powershell
wsl --set-default-version 2
```

### **Passo 3: Instalar Docker Desktop**
1. Baixe [Docker Desktop for Windows](https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe)
2. Execute o instalador
3. **IMPORTANTE**: Marque "Use WSL 2 instead of Hyper-V" durante instalação
4. Reinicie o sistema quando solicitado

### **Passo 4: Configurar Docker Desktop**
1. Abra Docker Desktop
2. Vá em **Settings** → **General**
3. Certifique-se que "Use the WSL 2 based engine" está marcado
4. Vá em **Settings** → **Resources** → **WSL Integration**
5. Habilite integração com distribuições WSL desejadas
6. Clique **Apply & Restart**

---

## ✅ **Verificação da Instalação**

### **Teste 1: Comando Docker**
```powershell
docker --version
# Esperado: Docker version 24.x.x, build xxxxxxx
```

### **Teste 2: Docker Daemon**
```powershell
docker info
# Deve mostrar informações do daemon sem erros
```

### **Teste 3: Container de Teste**
```powershell
docker run hello-world
# Deve baixar e executar container de teste com sucesso
```

### **Teste 4: Imagens Necessárias para Testes**
```powershell
# Baixar imagens usadas nos integration tests
docker pull nginx:alpine
docker pull redis:alpine

# Verificar imagens
docker images
```

---

## 🔧 **Configuração para Auto-Instalador Desktop**

### **Named Pipe Configuration (Windows)**
O Auto-Instalador usa Docker.DotNet que se conecta via named pipe no Windows:

**URI padrão**: `npipe://./pipe/docker_engine`

### **Verificar Conectividade Docker.DotNet**
Execute este teste C# para verificar conectividade:

```csharp
using Docker.DotNet;

var client = new DockerClientConfiguration().CreateClient();
try 
{
    var version = await client.System.GetVersionAsync();
    Console.WriteLine($"Docker conectado: {version.Version}");
}
catch (Exception ex)
{
    Console.WriteLine($"Erro: {ex.Message}");
}
```

---

## 🧪 **Executar Integration Tests**

### **Comando para Executar Testes**
```powershell
# Navegar para o diretório do projeto
cd c:\Users\<USER>\Projetos_IA\auto-instalador-max

# Executar apenas integration tests
dotnet test tests/AutoInstaller.Integration.Tests --verbosity normal

# Executar com filtro específico para Docker
dotnet test tests/AutoInstaller.Integration.Tests --filter "FullyQualifiedName~Docker" --verbosity normal
```

### **Resultado Esperado**
```
Total de testes: 21
     Aprovados: 21
     Com falha: 0
```

---

## 🔍 **Troubleshooting**

### **Problema: "Docker daemon is not running"**
**Solução:**
1. Abra Docker Desktop
2. Aguarde inicialização completa (ícone na bandeja sem animação)
3. Execute `docker info` para confirmar

### **Problema: "Access denied" ou "Permission denied"**
**Solução:**
1. Adicione usuário ao grupo "docker-users":
```powershell
# Execute como Administrador
net localgroup docker-users "SEU_USUARIO" /add
```
2. Faça logout/login ou reinicie

### **Problema: "No such host is known (docker:2376)"**
**Solução:**
1. Verifique se Docker Desktop está executando
2. Confirme configuração named pipe: `npipe://./pipe/docker_engine`
3. Teste conectividade com `docker info`

### **Problema: WSL 2 não funciona**
**Solução:**
1. Habilite Hyper-V como alternativa:
```powershell
# Execute como Administrador
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
```
2. Configure Docker Desktop para usar Hyper-V backend

---

## 📊 **Configurações de Performance**

### **Docker Desktop Settings**
- **Memory**: 4GB mínimo (8GB recomendado)
- **CPUs**: 2 cores mínimo (4 cores recomendado)
- **Disk image size**: 60GB mínimo
- **Swap**: 1GB

### **WSL 2 Settings (.wslconfig)**
Crie arquivo `C:\Users\<USER>\.wslconfig`:
```ini
[wsl2]
memory=4GB
processors=2
swap=1GB
```

---

## 🎯 **Validação Final**

### **Checklist de Configuração**
- [ ] Docker Desktop instalado e executando
- [ ] `docker --version` funciona
- [ ] `docker info` mostra daemon ativo
- [ ] `docker run hello-world` executa com sucesso
- [ ] Imagens `nginx:alpine` e `redis:alpine` baixadas
- [ ] Integration tests executam sem falhas
- [ ] Auto-Instalador Desktop conecta ao Docker via Docker.DotNet

### **Comando de Validação Completa**
```powershell
# Teste completo do ambiente
docker --version && docker info && docker run --rm hello-world && docker images && dotnet test tests/AutoInstaller.Integration.Tests --verbosity normal
```

---

## 📚 **Recursos Adicionais**

- [Docker Desktop Documentation](https://docs.docker.com/desktop/windows/)
- [WSL 2 Documentation](https://docs.microsoft.com/en-us/windows/wsl/install)
- [Docker.DotNet GitHub](https://github.com/dotnet/Docker.DotNet)
- [Auto-Instalador Desktop Repository](./README.md)

---

**Nota**: Este guia foi criado especificamente para o projeto Auto-Instalador Desktop. Para suporte adicional, consulte a documentação oficial do Docker ou abra uma issue no repositório do projeto.
