using MediatR;
using Microsoft.Extensions.Logging;
using AutoInstaller.Application.Commands;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces.Repositories;
using AutoInstaller.Core.Interfaces.Services;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Application.Handlers;

/// <summary>
/// Handler for creating Docker containers
/// </summary>
public sealed class CreateContainerHandler : IRequestHandler<CreateContainerCommand, CreateContainerResult>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IDockerService _dockerService;
    private readonly ILogger<CreateContainerHandler> _logger;
    private readonly IMediator _mediator;

    /// <summary>
    /// Initialize the handler
    /// </summary>
    /// <param name="containerRepository">Container repository</param>
    /// <param name="dockerService">Docker service</param>
    /// <param name="logger">Logger</param>
    /// <param name="mediator">Mediator for publishing events</param>
    public CreateContainerHandler(
        IContainerRepository containerRepository,
        IDockerService dockerService,
        ILogger<CreateContainerHandler> logger,
        IMediator mediator)
    {
        _containerRepository = containerRepository;
        _dockerService = dockerService;
        _logger = logger;
        _mediator = mediator;
    }

    /// <summary>
    /// Handle the create container command
    /// </summary>
    /// <param name="request">Create container command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Create container result</returns>
    public async Task<CreateContainerResult> Handle(CreateContainerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating container {ContainerName} with image {ImageTag}", 
                request.Name, request.ImageTag);

            // Validate container name availability
            if (!await _containerRepository.IsNameAvailableAsync(request.Name, cancellationToken: cancellationToken))
            {
                _logger.LogWarning("Container name {ContainerName} is already in use", request.Name);
                return new CreateContainerResult
                {
                    ContainerId = string.Empty,
                    Name = request.Name,
                    ImageTag = request.ImageTag,
                    Status = "Failed",
                    IsStarted = false,
                    CreatedAt = DateTime.UtcNow,
                    Success = false,
                    ErrorMessage = $"Container name '{request.Name}' is already in use"
                };
            }

            // Parse and validate image tag
            var imageTag = ImageTag.From(request.ImageTag);

            // Convert DTOs to value objects
            var portMappings = request.PortMappings.Select(ConvertToPortMapping).ToList();
            var volumeMounts = request.VolumeMounts.Select(ConvertToVolumeMount).ToList();
            var environmentVariables = request.EnvironmentVariables.Select(ConvertToEnvironmentVariable).ToList();

            // Validate configuration
            var validationResult = await _dockerService.ValidateContainerConfigurationAsync(
                request.Name,
                imageTag,
                portMappings,
                volumeMounts,
                environmentVariables,
                cancellationToken);

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Container configuration validation failed for {ContainerName}: {Errors}", 
                    request.Name, string.Join(", ", validationResult.Errors));
                
                return new CreateContainerResult
                {
                    ContainerId = string.Empty,
                    Name = request.Name,
                    ImageTag = request.ImageTag,
                    Status = "Failed",
                    IsStarted = false,
                    CreatedAt = DateTime.UtcNow,
                    Success = false,
                    ErrorMessage = $"Configuration validation failed: {string.Join(", ", validationResult.Errors)}"
                };
            }

            // Create container entity
            var container = Container.Create(request.Name, imageTag, request.Description);

            // Add port mappings
            foreach (var portMapping in portMappings)
            {
                container.AddPortMapping(portMapping);
            }

            // Add volume mounts
            foreach (var volumeMount in volumeMounts)
            {
                container.AddVolumeMount(volumeMount);
            }

            // Add environment variables
            foreach (var envVar in environmentVariables)
            {
                container.AddEnvironmentVariable(envVar);
            }

            // Save container to repository
            await _containerRepository.AddAsync(container, cancellationToken);

            // Publish domain events
            foreach (var domainEvent in container.DomainEvents)
            {
                await _mediator.Publish(domainEvent, cancellationToken);
            }

            container.ClearDomainEvents();

            var result = new CreateContainerResult
            {
                ContainerId = container.Id.Value,
                Name = container.Name,
                ImageTag = container.ImageTag.Value,
                Status = container.Status.ToString(),
                IsStarted = false,
                CreatedAt = container.CreatedAt,
                Success = true
            };

            // Start container if requested
            if (request.StartImmediately)
            {
                try
                {
                    container.Start();
                    await _containerRepository.UpdateAsync(container, cancellationToken);

                    // Publish start events
                    foreach (var domainEvent in container.DomainEvents)
                    {
                        await _mediator.Publish(domainEvent, cancellationToken);
                    }

                    container.ClearDomainEvents();

                    result = result with 
                    { 
                        Status = container.Status.ToString(),
                        IsStarted = true 
                    };

                    _logger.LogInformation("Container {ContainerName} created and started successfully", request.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Container {ContainerName} created but failed to start: {Error}", 
                        request.Name, ex.Message);
                    
                    result = result with 
                    { 
                        ErrorMessage = $"Container created but failed to start: {ex.Message}" 
                    };
                }
            }
            else
            {
                _logger.LogInformation("Container {ContainerName} created successfully", request.Name);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create container {ContainerName}: {Error}", request.Name, ex.Message);
            
            return new CreateContainerResult
            {
                ContainerId = string.Empty,
                Name = request.Name,
                ImageTag = request.ImageTag,
                Status = "Failed",
                IsStarted = false,
                CreatedAt = DateTime.UtcNow,
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Convert DTO to PortMapping value object
    /// </summary>
    /// <param name="dto">Port mapping DTO</param>
    /// <returns>PortMapping value object</returns>
    private static PortMapping ConvertToPortMapping(CreatePortMappingDto dto)
    {
        var protocol = Enum.Parse<PortProtocol>(dto.Protocol, ignoreCase: true);
        return PortMapping.Create(dto.HostPort, dto.ContainerPort, protocol, dto.HostIP);
    }

    /// <summary>
    /// Convert DTO to VolumeMount value object
    /// </summary>
    /// <param name="dto">Volume mount DTO</param>
    /// <returns>VolumeMount value object</returns>
    private static VolumeMount ConvertToVolumeMount(CreateVolumeMountDto dto)
    {
        var mountType = Enum.Parse<MountType>(dto.Type, ignoreCase: true);
        return VolumeMount.Create(dto.Source, dto.ContainerPath, mountType, dto.IsReadOnly);
    }

    /// <summary>
    /// Convert DTO to EnvironmentVariable value object
    /// </summary>
    /// <param name="dto">Environment variable DTO</param>
    /// <returns>EnvironmentVariable value object</returns>
    private static EnvironmentVariable ConvertToEnvironmentVariable(CreateEnvironmentVariableDto dto)
    {
        return EnvironmentVariable.Create(dto.Name, dto.Value, dto.IsSensitive);
    }
}
