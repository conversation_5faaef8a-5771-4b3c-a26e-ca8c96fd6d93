using MediatR;

namespace AutoInstaller.Core.Events;

/// <summary>
/// Base class for all domain events
/// </summary>
public abstract record DomainEvent : INotification
{
    /// <summary>
    /// Event unique identifier
    /// </summary>
    public Guid Id { get; } = Guid.NewGuid();

    /// <summary>
    /// Event occurrence timestamp
    /// </summary>
    public DateTime OccurredAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// Event version for compatibility
    /// </summary>
    public virtual int Version => 1;

    /// <summary>
    /// Event type name
    /// </summary>
    public string EventType => GetType().Name;
}
