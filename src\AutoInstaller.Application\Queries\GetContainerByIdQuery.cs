using MediatR;

namespace AutoInstaller.Application.Queries;

/// <summary>
/// Query to get a container by ID
/// </summary>
public sealed record GetContainerByIdQuery : IRequest<ContainerDto?>
{
    /// <summary>
    /// Container ID
    /// </summary>
    public required string ContainerId { get; init; }
}

/// <summary>
/// Query to get a container by name
/// </summary>
public sealed record GetContainerByNameQuery : IRequest<ContainerDto?>
{
    /// <summary>
    /// Container name
    /// </summary>
    public required string Name { get; init; }
}

/// <summary>
/// Query to get all containers
/// </summary>
public sealed record GetAllContainersQuery : IRequest<IReadOnlyList<ContainerDto>>
{
    /// <summary>
    /// Include stopped containers
    /// </summary>
    public bool IncludeStopped { get; init; } = true;

    /// <summary>
    /// Include removed containers
    /// </summary>
    public bool IncludeRemoved { get; init; } = false;
}

/// <summary>
/// Query to get containers with pagination
/// </summary>
public sealed record GetContainersPagedQuery : IRequest<PagedResult<ContainerDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; init; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; init; } = 20;

    /// <summary>
    /// Filter by status
    /// </summary>
    public string? StatusFilter { get; init; }

    /// <summary>
    /// Search by name pattern
    /// </summary>
    public string? NamePattern { get; init; }

    /// <summary>
    /// Filter by image
    /// </summary>
    public string? ImageFilter { get; init; }
}

/// <summary>
/// Query to get running containers
/// </summary>
public sealed record GetRunningContainersQuery : IRequest<IReadOnlyList<ContainerDto>>
{
}

/// <summary>
/// Query to get container statistics
/// </summary>
public sealed record GetContainerStatsQuery : IRequest<ContainerStatsDto>
{
    /// <summary>
    /// Container ID or name
    /// </summary>
    public required string ContainerIdOrName { get; init; }
}

/// <summary>
/// Container DTO
/// </summary>
public sealed record ContainerDto
{
    /// <summary>
    /// Container ID
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// Container name
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// Image tag
    /// </summary>
    public required string ImageTag { get; init; }

    /// <summary>
    /// Container status
    /// </summary>
    public required string Status { get; init; }

    /// <summary>
    /// Status display name
    /// </summary>
    public required string StatusDisplayName { get; init; }

    /// <summary>
    /// Status color for UI
    /// </summary>
    public required string StatusColor { get; init; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public required DateTime CreatedAt { get; init; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public required DateTime UpdatedAt { get; init; }

    /// <summary>
    /// Container description
    /// </summary>
    public string? Description { get; init; }

    /// <summary>
    /// Port mappings
    /// </summary>
    public IReadOnlyList<PortMappingDto> PortMappings { get; init; } = Array.Empty<PortMappingDto>();

    /// <summary>
    /// Volume mounts
    /// </summary>
    public IReadOnlyList<VolumeMountDto> VolumeMounts { get; init; } = Array.Empty<VolumeMountDto>();

    /// <summary>
    /// Environment variables
    /// </summary>
    public IReadOnlyList<EnvironmentVariableDto> EnvironmentVariables { get; init; } = Array.Empty<EnvironmentVariableDto>();

    /// <summary>
    /// Connected networks
    /// </summary>
    public IReadOnlyList<string> Networks { get; init; } = Array.Empty<string>();
}

/// <summary>
/// Port mapping DTO
/// </summary>
public sealed record PortMappingDto
{
    /// <summary>
    /// Host port
    /// </summary>
    public required int HostPort { get; init; }

    /// <summary>
    /// Container port
    /// </summary>
    public required int ContainerPort { get; init; }

    /// <summary>
    /// Protocol
    /// </summary>
    public required string Protocol { get; init; }

    /// <summary>
    /// Host IP
    /// </summary>
    public string? HostIP { get; init; }

    /// <summary>
    /// Display string
    /// </summary>
    public required string DisplayString { get; init; }
}

/// <summary>
/// Volume mount DTO
/// </summary>
public sealed record VolumeMountDto
{
    /// <summary>
    /// Source
    /// </summary>
    public required string Source { get; init; }

    /// <summary>
    /// Container path
    /// </summary>
    public required string ContainerPath { get; init; }

    /// <summary>
    /// Mount type
    /// </summary>
    public required string Type { get; init; }

    /// <summary>
    /// Is read-only
    /// </summary>
    public required bool IsReadOnly { get; init; }

    /// <summary>
    /// Display string
    /// </summary>
    public required string DisplayString { get; init; }
}

/// <summary>
/// Environment variable DTO
/// </summary>
public sealed record EnvironmentVariableDto
{
    /// <summary>
    /// Variable name
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// Variable value (masked if sensitive)
    /// </summary>
    public required string Value { get; init; }

    /// <summary>
    /// Is sensitive
    /// </summary>
    public required bool IsSensitive { get; init; }

    /// <summary>
    /// Display string
    /// </summary>
    public required string DisplayString { get; init; }
}

/// <summary>
/// Container statistics DTO
/// </summary>
public sealed record ContainerStatsDto
{
    /// <summary>
    /// Container ID
    /// </summary>
    public required string ContainerId { get; init; }

    /// <summary>
    /// Container name
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// CPU usage percentage
    /// </summary>
    public required double CpuUsagePercent { get; init; }

    /// <summary>
    /// Memory usage in bytes
    /// </summary>
    public required long MemoryUsageBytes { get; init; }

    /// <summary>
    /// Memory limit in bytes
    /// </summary>
    public required long MemoryLimitBytes { get; init; }

    /// <summary>
    /// Memory usage percentage
    /// </summary>
    public required double MemoryUsagePercent { get; init; }

    /// <summary>
    /// Network input bytes
    /// </summary>
    public required long NetworkInputBytes { get; init; }

    /// <summary>
    /// Network output bytes
    /// </summary>
    public required long NetworkOutputBytes { get; init; }

    /// <summary>
    /// Block input bytes
    /// </summary>
    public required long BlockInputBytes { get; init; }

    /// <summary>
    /// Block output bytes
    /// </summary>
    public required long BlockOutputBytes { get; init; }

    /// <summary>
    /// Statistics timestamp
    /// </summary>
    public required DateTime Timestamp { get; init; }
}

/// <summary>
/// Paginated result
/// </summary>
/// <typeparam name="T">Item type</typeparam>
public sealed record PagedResult<T>
{
    /// <summary>
    /// Items in current page
    /// </summary>
    public required IReadOnlyList<T> Items { get; init; }

    /// <summary>
    /// Total number of items
    /// </summary>
    public required int TotalCount { get; init; }

    /// <summary>
    /// Current page number
    /// </summary>
    public required int PageNumber { get; init; }

    /// <summary>
    /// Page size
    /// </summary>
    public required int PageSize { get; init; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Has previous page
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Has next page
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;
}
