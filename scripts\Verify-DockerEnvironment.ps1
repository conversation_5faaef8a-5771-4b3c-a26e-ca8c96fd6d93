# Verify-DockerEnvironment.ps1
# Script para verificação automática do ambiente Docker para Auto-Instalador Desktop

param(
    [switch]$InstallImages = $false,
    [switch]$RunTests = $false,
    [switch]$Detailed = $false
)

Write-Host "🐳 Verificação do Ambiente Docker - Auto-Instalador Desktop" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

$ErrorCount = 0
$WarningCount = 0

function Write-Success($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-Error($message) {
    Write-Host "❌ $message" -ForegroundColor Red
    $script:ErrorCount++
}

function Write-Warning($message) {
    Write-Host "⚠️  $message" -ForegroundColor Yellow
    $script:WarningCount++
}

function Write-Info($message) {
    Write-Host "ℹ️  $message" -ForegroundColor Blue
}

function Write-Step($message) {
    Write-Host "🔍 $message" -ForegroundColor White
}

# Verificação 1: Docker Command
Write-Step "Verificando comando Docker..."
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker CLI encontrado: $dockerVersion"
    } else {
        Write-Error "Docker CLI não encontrado. Instale Docker Desktop."
        Write-Info "Download: https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
    }
} catch {
    Write-Error "Docker CLI não encontrado. Instale Docker Desktop."
    Write-Info "Download: https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
}

# Verificação 2: Docker Daemon
Write-Step "Verificando Docker Daemon..."
try {
    $dockerInfo = docker info --format "{{.ServerVersion}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker Daemon ativo: versão $dockerInfo"
    } else {
        Write-Error "Docker Daemon não está executando. Inicie Docker Desktop."
    }
} catch {
    Write-Error "Docker Daemon não está executando. Inicie Docker Desktop."
}

# Verificação 3: Conectividade Named Pipe (Windows)
Write-Step "Verificando conectividade Named Pipe..."
$namedPipePath = "\\.\pipe\docker_engine"
if (Test-Path $namedPipePath) {
    Write-Success "Named Pipe Docker acessível: $namedPipePath"
} else {
    Write-Warning "Named Pipe Docker não encontrado. Docker Desktop pode não estar totalmente inicializado."
}

# Verificação 4: Teste de Container
Write-Step "Testando execução de container..."
try {
    $testResult = docker run --rm hello-world 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Container de teste executado com sucesso"
    } else {
        Write-Error "Falha ao executar container de teste"
    }
} catch {
    Write-Error "Falha ao executar container de teste"
}

# Verificação 5: Imagens Necessárias
Write-Step "Verificando imagens necessárias para testes..."
$requiredImages = @("nginx:alpine", "redis:alpine")
$missingImages = @()

foreach ($image in $requiredImages) {
    try {
        $imageExists = docker images --format "{{.Repository}}:{{.Tag}}" | Select-String -Pattern "^$image$"
        if ($imageExists) {
            Write-Success "Imagem encontrada: $image"
        } else {
            Write-Warning "Imagem não encontrada: $image"
            $missingImages += $image
        }
    } catch {
        Write-Warning "Erro ao verificar imagem: $image"
        $missingImages += $image
    }
}

# Instalação de imagens se solicitado
if ($InstallImages -and $missingImages.Count -gt 0) {
    Write-Step "Baixando imagens necessárias..."
    foreach ($image in $missingImages) {
        Write-Info "Baixando $image..."
        try {
            docker pull $image
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Imagem baixada: $image"
            } else {
                Write-Error "Falha ao baixar imagem: $image"
            }
        } catch {
            Write-Error "Falha ao baixar imagem: $image"
        }
    }
}

# Verificação 6: Recursos do Sistema
Write-Step "Verificando recursos do sistema..."
try {
    $dockerStats = docker system df --format "table {{.Type}}\t{{.TotalCount}}\t{{.Size}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Recursos Docker disponíveis"
        if ($Detailed) {
            Write-Info "Estatísticas Docker:"
            Write-Host $dockerStats -ForegroundColor Gray
        }
    }
} catch {
    Write-Warning "Não foi possível obter estatísticas do Docker"
}

# Verificação 7: Configuração Docker Desktop
Write-Step "Verificando configuração Docker Desktop..."
try {
    $dockerContext = docker context show 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Contexto Docker ativo: $dockerContext"
    }
    
    $dockerVersion = docker version --format "{{.Server.Os}}/{{.Server.Arch}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Plataforma Docker: $dockerVersion"
    }
} catch {
    Write-Warning "Não foi possível obter informações detalhadas do Docker"
}

# Verificação 8: Testes de Integration (se solicitado)
if ($RunTests) {
    Write-Step "Executando Integration Tests..."
    try {
        $testPath = "tests/AutoInstaller.Integration.Tests"
        if (Test-Path $testPath) {
            Write-Info "Executando testes em $testPath..."
            $testResult = dotnet test $testPath --verbosity minimal --nologo
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Integration Tests executados com sucesso"
            } else {
                Write-Error "Falhas nos Integration Tests"
                Write-Info "Execute 'dotnet test $testPath --verbosity normal' para detalhes"
            }
        } else {
            Write-Warning "Diretório de testes não encontrado: $testPath"
        }
    } catch {
        Write-Error "Erro ao executar Integration Tests"
    }
}

# Resumo Final
Write-Host ""
Write-Host "📊 Resumo da Verificação" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

if ($ErrorCount -eq 0 -and $WarningCount -eq 0) {
    Write-Host "🎉 Ambiente Docker configurado perfeitamente!" -ForegroundColor Green
    Write-Host "   Todos os Integration Tests devem executar com sucesso." -ForegroundColor Green
} elseif ($ErrorCount -eq 0) {
    Write-Host "✅ Ambiente Docker funcional com $WarningCount aviso(s)" -ForegroundColor Yellow
    Write-Host "   Integration Tests devem executar, mas considere resolver os avisos." -ForegroundColor Yellow
} else {
    Write-Host "❌ Ambiente Docker com $ErrorCount erro(s) e $WarningCount aviso(s)" -ForegroundColor Red
    Write-Host "   Integration Tests falharão. Resolva os erros antes de continuar." -ForegroundColor Red
}

Write-Host ""
Write-Host "📚 Recursos Úteis:" -ForegroundColor Cyan
Write-Host "   • Guia completo: docs/Docker-Setup-Guide.md" -ForegroundColor Gray
Write-Host "   • Executar com imagens: .\scripts\Verify-DockerEnvironment.ps1 -InstallImages" -ForegroundColor Gray
Write-Host "   • Executar com testes: .\scripts\Verify-DockerEnvironment.ps1 -RunTests" -ForegroundColor Gray
Write-Host "   • Modo detalhado: .\scripts\Verify-DockerEnvironment.ps1 -Detailed" -ForegroundColor Gray

# Códigos de saída
if ($ErrorCount -gt 0) {
    exit 1
} elseif ($WarningCount -gt 0) {
    exit 2
} else {
    exit 0
}
