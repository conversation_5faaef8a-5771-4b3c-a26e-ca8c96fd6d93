namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker port mapping
/// </summary>
public sealed record PortMapping
{
    /// <summary>
    /// Host port number
    /// </summary>
    public int HostPort { get; }

    /// <summary>
    /// Container port number
    /// </summary>
    public int ContainerPort { get; }

    /// <summary>
    /// Protocol (TCP or UDP)
    /// </summary>
    public PortProtocol Protocol { get; }

    /// <summary>
    /// Host IP address to bind to (optional)
    /// </summary>
    public string? HostIP { get; }

    /// <summary>
    /// Initialize a new port mapping
    /// </summary>
    /// <param name="hostPort">Host port number</param>
    /// <param name="containerPort">Container port number</param>
    /// <param name="protocol">Port protocol</param>
    /// <param name="hostIP">Host IP address</param>
    private PortMapping(int hostPort, int containerPort, PortProtocol protocol, string? hostIP)
    {
        HostPort = hostPort;
        ContainerPort = containerPort;
        Protocol = protocol;
        HostIP = hostIP;
    }

    /// <summary>
    /// Create a new port mapping
    /// </summary>
    /// <param name="hostPort">Host port number</param>
    /// <param name="containerPort">Container port number</param>
    /// <param name="protocol">Port protocol (default: TCP)</param>
    /// <param name="hostIP">Host IP address (default: all interfaces)</param>
    /// <returns>Port mapping instance</returns>
    /// <exception cref="ArgumentException">Thrown when port numbers are invalid</exception>
    public static PortMapping Create(int hostPort, int containerPort, PortProtocol protocol = PortProtocol.TCP, string? hostIP = null)
    {
        ValidatePort(hostPort, nameof(hostPort));
        ValidatePort(containerPort, nameof(containerPort));

        if (!string.IsNullOrWhiteSpace(hostIP))
        {
            ValidateIPAddress(hostIP);
        }

        return new PortMapping(hostPort, containerPort, protocol, hostIP);
    }

    /// <summary>
    /// Create port mapping from string format (e.g., "8080:80/tcp", "127.0.0.1:8080:80/udp")
    /// </summary>
    /// <param name="portString">Port mapping string</param>
    /// <returns>Port mapping instance</returns>
    /// <exception cref="ArgumentException">Thrown when format is invalid</exception>
    public static PortMapping FromString(string portString)
    {
        if (string.IsNullOrWhiteSpace(portString))
            throw new ArgumentException("Port string cannot be null or empty", nameof(portString));

        // Parse protocol
        var protocol = PortProtocol.TCP;
        if (portString.EndsWith("/udp", StringComparison.OrdinalIgnoreCase))
        {
            protocol = PortProtocol.UDP;
            portString = portString[..^4];
        }
        else if (portString.EndsWith("/tcp", StringComparison.OrdinalIgnoreCase))
        {
            portString = portString[..^4];
        }

        // Parse IP and ports
        string? hostIP = null;
        string portPart = portString;

        // Check for IP address
        var colonCount = portString.Count(c => c == ':');
        if (colonCount == 2)
        {
            var parts = portString.Split(':', 3);
            hostIP = parts[0];
            portPart = $"{parts[1]}:{parts[2]}";
        }

        // Parse host:container ports
        var portParts = portPart.Split(':');
        if (portParts.Length != 2)
            throw new ArgumentException("Port string must be in format 'hostPort:containerPort' or 'hostIP:hostPort:containerPort'", nameof(portString));

        if (!int.TryParse(portParts[0], out var hostPort))
            throw new ArgumentException("Invalid host port number", nameof(portString));

        if (!int.TryParse(portParts[1], out var containerPort))
            throw new ArgumentException("Invalid container port number", nameof(portString));

        return Create(hostPort, containerPort, protocol, hostIP);
    }

    /// <summary>
    /// Check if this is a privileged port (less than 1024)
    /// </summary>
    /// <returns>True if host port is privileged</returns>
    public bool IsPrivilegedPort()
    {
        return HostPort < 1024;
    }

    /// <summary>
    /// Check if ports are the same (host and container)
    /// </summary>
    /// <returns>True if host and container ports match</returns>
    public bool IsSamePort()
    {
        return HostPort == ContainerPort;
    }

    /// <summary>
    /// Get string representation in Docker format
    /// </summary>
    /// <returns>Port mapping string</returns>
    public string ToDockerString()
    {
        var protocolSuffix = Protocol == PortProtocol.UDP ? "/udp" : "";
        
        if (!string.IsNullOrWhiteSpace(HostIP))
        {
            return $"{HostIP}:{HostPort}:{ContainerPort}{protocolSuffix}";
        }
        
        return $"{HostPort}:{ContainerPort}{protocolSuffix}";
    }

    /// <summary>
    /// Validate port number
    /// </summary>
    /// <param name="port">Port number to validate</param>
    /// <param name="paramName">Parameter name for exception</param>
    /// <exception cref="ArgumentException">Thrown when port is invalid</exception>
    private static void ValidatePort(int port, string paramName)
    {
        if (port < 1 || port > 65535)
            throw new ArgumentException("Port number must be between 1 and 65535", paramName);
    }

    /// <summary>
    /// Validate IP address format
    /// </summary>
    /// <param name="ipAddress">IP address to validate</param>
    /// <exception cref="ArgumentException">Thrown when IP address is invalid</exception>
    private static void ValidateIPAddress(string ipAddress)
    {
        if (!System.Net.IPAddress.TryParse(ipAddress, out _))
            throw new ArgumentException("Invalid IP address format", nameof(ipAddress));
    }

    /// <summary>
    /// String representation of port mapping
    /// </summary>
    /// <returns>Port mapping string</returns>
    public override string ToString() => ToDockerString();
}

/// <summary>
/// Port protocol enumeration
/// </summary>
public enum PortProtocol
{
    /// <summary>
    /// TCP protocol
    /// </summary>
    TCP,
    
    /// <summary>
    /// UDP protocol
    /// </summary>
    UDP
}
