using FluentValidation;
using AutoInstaller.Application.Commands;

namespace AutoInstaller.Application.Validators;

/// <summary>
/// Validator for CreateContainerCommand
/// </summary>
public sealed class CreateContainerCommandValidator : AbstractValidator<CreateContainerCommand>
{
    /// <summary>
    /// Initialize the validator
    /// </summary>
    public CreateContainerCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Container name is required")
            .Length(1, 255)
            .WithMessage("Container name must be between 1 and 255 characters")
            .Matches(@"^[a-zA-Z0-9][a-zA-Z0-9_.-]*$")
            .WithMessage("Container name must start with alphanumeric character and contain only letters, numbers, underscores, periods, and hyphens");

        RuleFor(x => x.ImageTag)
            .NotEmpty()
            .WithMessage("Image tag is required")
            .Must(BeValidImageTag)
            .WithMessage("Image tag must be in valid format (repository:tag)");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters");

        RuleForEach(x => x.PortMappings)
            .SetValidator(new CreatePortMappingDtoValidator());

        RuleForEach(x => x.VolumeMounts)
            .SetValidator(new CreateVolumeMountDtoValidator());

        RuleForEach(x => x.EnvironmentVariables)
            .SetValidator(new CreateEnvironmentVariableDtoValidator());

        RuleFor(x => x.Networks)
            .Must(networks => networks.All(n => !string.IsNullOrWhiteSpace(n)))
            .WithMessage("Network names cannot be empty");

        // Custom validation for port conflicts
        RuleFor(x => x.PortMappings)
            .Must(HaveUniqueHostPorts)
            .WithMessage("Host ports must be unique");
    }

    /// <summary>
    /// Validate image tag format
    /// </summary>
    /// <param name="imageTag">Image tag to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidImageTag(string imageTag)
    {
        if (string.IsNullOrWhiteSpace(imageTag))
            return false;

        try
        {
            // Try to parse as ImageTag to validate format
            var _ = Core.ValueObjects.ImageTag.From(imageTag);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Check if host ports are unique
    /// </summary>
    /// <param name="portMappings">Port mappings to check</param>
    /// <returns>True if all host ports are unique</returns>
    private static bool HaveUniqueHostPorts(IReadOnlyList<CreatePortMappingDto> portMappings)
    {
        var hostPorts = portMappings.Select(p => p.HostPort).ToList();
        return hostPorts.Count == hostPorts.Distinct().Count();
    }
}

/// <summary>
/// Validator for CreatePortMappingDto
/// </summary>
public sealed class CreatePortMappingDtoValidator : AbstractValidator<CreatePortMappingDto>
{
    /// <summary>
    /// Initialize the validator
    /// </summary>
    public CreatePortMappingDtoValidator()
    {
        RuleFor(x => x.HostPort)
            .InclusiveBetween(1, 65535)
            .WithMessage("Host port must be between 1 and 65535");

        RuleFor(x => x.ContainerPort)
            .InclusiveBetween(1, 65535)
            .WithMessage("Container port must be between 1 and 65535");

        RuleFor(x => x.Protocol)
            .NotEmpty()
            .WithMessage("Protocol is required")
            .Must(BeValidProtocol)
            .WithMessage("Protocol must be TCP or UDP");

        RuleFor(x => x.HostIP)
            .Must(BeValidIPAddress)
            .When(x => !string.IsNullOrWhiteSpace(x.HostIP))
            .WithMessage("Host IP must be a valid IP address");
    }

    /// <summary>
    /// Validate protocol
    /// </summary>
    /// <param name="protocol">Protocol to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidProtocol(string protocol)
    {
        return Enum.TryParse<Core.ValueObjects.PortProtocol>(protocol, ignoreCase: true, out _);
    }

    /// <summary>
    /// Validate IP address
    /// </summary>
    /// <param name="ipAddress">IP address to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidIPAddress(string? ipAddress)
    {
        if (string.IsNullOrWhiteSpace(ipAddress))
            return true;

        return System.Net.IPAddress.TryParse(ipAddress, out _);
    }
}

/// <summary>
/// Validator for CreateVolumeMountDto
/// </summary>
public sealed class CreateVolumeMountDtoValidator : AbstractValidator<CreateVolumeMountDto>
{
    /// <summary>
    /// Initialize the validator
    /// </summary>
    public CreateVolumeMountDtoValidator()
    {
        RuleFor(x => x.Source)
            .NotEmpty()
            .WithMessage("Volume source is required")
            .MaximumLength(500)
            .WithMessage("Volume source cannot exceed 500 characters");

        RuleFor(x => x.ContainerPath)
            .NotEmpty()
            .WithMessage("Container path is required")
            .MaximumLength(500)
            .WithMessage("Container path cannot exceed 500 characters")
            .Must(BeAbsolutePath)
            .WithMessage("Container path must be absolute");

        RuleFor(x => x.Type)
            .NotEmpty()
            .WithMessage("Mount type is required")
            .Must(BeValidMountType)
            .WithMessage("Mount type must be bind, volume, or tmpfs");
    }

    /// <summary>
    /// Check if path is absolute
    /// </summary>
    /// <param name="path">Path to check</param>
    /// <returns>True if absolute</returns>
    private static bool BeAbsolutePath(string path)
    {
        return Path.IsPathRooted(path) || path.StartsWith('/');
    }

    /// <summary>
    /// Validate mount type
    /// </summary>
    /// <param name="mountType">Mount type to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidMountType(string mountType)
    {
        return Enum.TryParse<Core.ValueObjects.MountType>(mountType, ignoreCase: true, out _);
    }
}

/// <summary>
/// Validator for CreateEnvironmentVariableDto
/// </summary>
public sealed class CreateEnvironmentVariableDtoValidator : AbstractValidator<CreateEnvironmentVariableDto>
{
    /// <summary>
    /// Initialize the validator
    /// </summary>
    public CreateEnvironmentVariableDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Environment variable name is required")
            .MaximumLength(255)
            .WithMessage("Environment variable name cannot exceed 255 characters")
            .Matches(@"^[a-zA-Z_][a-zA-Z0-9_]*$")
            .WithMessage("Environment variable name must start with letter or underscore and contain only letters, numbers, and underscores");

        RuleFor(x => x.Value)
            .NotNull()
            .WithMessage("Environment variable value cannot be null")
            .MaximumLength(32767)
            .WithMessage("Environment variable value cannot exceed 32767 characters");
    }
}
