using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.Infrastructure.Docker.Embedded.Resources;

/// <summary>
/// Extrator de binários Podman embarcados no assembly
/// </summary>
public class PodmanBinaryExtractor : IPodmanBinaryExtractor
{
    private readonly ILogger<PodmanBinaryExtractor> _logger;
    private readonly Assembly _assembly;
    private string? _extractedPath;
    private bool _disposed = false;

    // Mapeamento de arquivos necessários por plataforma
    private static readonly Dictionary<string, string[]> PlatformFiles = new()
    {
        ["windows"] = new[] { "podman.exe", "conmon.exe", "crun.exe" },
        ["linux"] = new[] { "podman", "conmon", "crun" },
        ["macos"] = new[] { "podman", "conmon", "crun" }
    };

    // Hashes esperados dos binários (para validação de integridade)
    private static readonly Dictionary<string, Dictionary<string, string>> FileHashes = new()
    {
        ["windows"] = new()
        {
            ["podman.exe"] = "sha256:placeholder_hash_podman_windows",
            ["conmon.exe"] = "sha256:placeholder_hash_conmon_windows", 
            ["crun.exe"] = "sha256:placeholder_hash_crun_windows"
        },
        ["linux"] = new()
        {
            ["podman"] = "sha256:placeholder_hash_podman_linux",
            ["conmon"] = "sha256:placeholder_hash_conmon_linux",
            ["crun"] = "sha256:placeholder_hash_crun_linux"
        },
        ["macos"] = new()
        {
            ["podman"] = "sha256:placeholder_hash_podman_macos",
            ["conmon"] = "sha256:placeholder_hash_conmon_macos",
            ["crun"] = "sha256:placeholder_hash_crun_macos"
        }
    };

    public PodmanBinaryExtractor(ILogger<PodmanBinaryExtractor> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _assembly = Assembly.GetExecutingAssembly();
    }

    public async Task<string> ExtractBinariesAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Extraindo binários Podman embarcados...");

        try
        {
            var platform = GetCurrentPlatform();
            var tempDir = Path.Combine(Path.GetTempPath(), "AutoInstaller", "Podman", Guid.NewGuid().ToString("N"));
            
            Directory.CreateDirectory(tempDir);
            _extractedPath = tempDir;

            _logger.LogDebug("Diretório de extração criado: {TempDir}", tempDir);

            var requiredFiles = PlatformFiles[platform];
            foreach (var fileName in requiredFiles)
            {
                await ExtractFileAsync(platform, fileName, tempDir, cancellationToken);
            }

            // Configurar permissões de execução no Unix
            if (platform != "windows")
            {
                await SetExecutablePermissionsAsync(tempDir, requiredFiles, cancellationToken);
            }

            // Validar integridade dos arquivos extraídos
            await ValidateExtractedFilesAsync(platform, tempDir, cancellationToken);

            _logger.LogInformation("Binários Podman extraídos com sucesso para: {TempDir}", tempDir);
            return tempDir;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Falha ao extrair binários Podman");
            
            // Cleanup em caso de erro
            if (!string.IsNullOrEmpty(_extractedPath) && Directory.Exists(_extractedPath))
            {
                try
                {
                    Directory.Delete(_extractedPath, recursive: true);
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning(cleanupEx, "Falha ao limpar diretório após erro");
                }
            }
            
            throw;
        }
    }

    public async Task<bool> AreBinariesExtractedAsync(string extractPath)
    {
        if (string.IsNullOrEmpty(extractPath) || !Directory.Exists(extractPath))
            return false;

        try
        {
            var platform = GetCurrentPlatform();
            var requiredFiles = PlatformFiles[platform];

            foreach (var fileName in requiredFiles)
            {
                var filePath = Path.Combine(extractPath, fileName);
                if (!File.Exists(filePath))
                {
                    _logger.LogDebug("Arquivo necessário não encontrado: {FilePath}", filePath);
                    return false;
                }

                // Verificar se arquivo não está corrompido (tamanho mínimo)
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length < 1024) // Mínimo 1KB
                {
                    _logger.LogDebug("Arquivo muito pequeno (possivelmente corrompido): {FilePath}", filePath);
                    return false;
                }
            }

            _logger.LogDebug("Todos os binários necessários estão presentes em: {ExtractPath}", extractPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao verificar binários extraídos");
            return false;
        }
    }

    public async Task CleanupExtractedBinariesAsync(CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_extractedPath))
            return;

        try
        {
            _logger.LogInformation("Limpando binários extraídos: {ExtractedPath}", _extractedPath);

            await Task.Run(() =>
            {
                if (Directory.Exists(_extractedPath))
                {
                    Directory.Delete(_extractedPath, recursive: true);
                }
            }, cancellationToken);

            _logger.LogDebug("Binários limpos com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Falha ao limpar binários extraídos");
        }
        finally
        {
            _extractedPath = null;
        }
    }

    public async Task<PodmanBinaryInfo> GetBinaryInfoAsync()
    {
        var platform = GetCurrentPlatform();
        var requiredFiles = PlatformFiles[platform];
        var hashes = FileHashes[platform];

        return new PodmanBinaryInfo
        {
            Version = "4.8.0", // Versão embarcada
            Platform = platform,
            Architecture = RuntimeInformation.ProcessArchitecture.ToString(),
            BuildDate = DateTime.UtcNow, // Placeholder - seria data real do build
            RequiredFiles = requiredFiles,
            FileHashes = hashes,
            TotalSize = await CalculateTotalSizeAsync(platform, requiredFiles)
        };
    }

    private async Task ExtractFileAsync(string platform, string fileName, string targetDir, CancellationToken cancellationToken)
    {
        var resourceName = $"AutoInstaller.Infrastructure.Docker.Embedded.Resources.Binaries.{platform}.{fileName}";
        var targetPath = Path.Combine(targetDir, fileName);

        _logger.LogDebug("Extraindo {FileName} para {TargetPath}", fileName, targetPath);

        using var resourceStream = _assembly.GetManifestResourceStream(resourceName);
        if (resourceStream == null)
        {
            throw new FileNotFoundException($"Recurso embarcado não encontrado: {resourceName}");
        }

        using var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write);
        await resourceStream.CopyToAsync(fileStream, cancellationToken);

        _logger.LogDebug("Arquivo extraído: {FileName} ({Size} bytes)", fileName, resourceStream.Length);
    }

    private async Task SetExecutablePermissionsAsync(string directory, string[] files, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Configurando permissões de execução para arquivos Unix");

        foreach (var fileName in files)
        {
            var filePath = Path.Combine(directory, fileName);
            
            try
            {
                // Usar chmod para definir permissões de execução (755)
                using var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "chmod",
                        Arguments = $"755 \"{filePath}\"",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync(cancellationToken);

                if (process.ExitCode != 0)
                {
                    _logger.LogWarning("Falha ao definir permissões para {FileName}", fileName);
                }
                else
                {
                    _logger.LogDebug("Permissões definidas para {FileName}", fileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao definir permissões para {FileName}", fileName);
            }
        }
    }

    private async Task ValidateExtractedFilesAsync(string platform, string directory, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Validando integridade dos arquivos extraídos");

        var expectedHashes = FileHashes[platform];
        
        foreach (var (fileName, expectedHash) in expectedHashes)
        {
            var filePath = Path.Combine(directory, fileName);
            
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"Arquivo extraído não encontrado: {filePath}");
            }

            // Para esta implementação inicial, apenas verificamos se o arquivo existe e tem tamanho > 0
            // Em produção, implementaríamos verificação de hash real
            var fileInfo = new FileInfo(filePath);
            if (fileInfo.Length == 0)
            {
                throw new InvalidDataException($"Arquivo extraído está vazio: {filePath}");
            }

            _logger.LogDebug("Arquivo validado: {FileName} ({Size} bytes)", fileName, fileInfo.Length);
        }
    }

    private async Task<long> CalculateTotalSizeAsync(string platform, string[] files)
    {
        long totalSize = 0;
        
        foreach (var fileName in files)
        {
            var resourceName = $"AutoInstaller.Infrastructure.Docker.Embedded.Resources.Binaries.{platform}.{fileName}";
            using var resourceStream = _assembly.GetManifestResourceStream(resourceName);
            if (resourceStream != null)
            {
                totalSize += resourceStream.Length;
            }
        }

        return totalSize;
    }

    private static string GetCurrentPlatform()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            return "windows";
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            return "linux";
        if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            return "macos";
        
        throw new PlatformNotSupportedException("Plataforma não suportada para Podman embarcado");
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            CleanupExtractedBinariesAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante cleanup no dispose");
        }

        _disposed = true;
    }
}
