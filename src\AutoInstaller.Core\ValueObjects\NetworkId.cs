namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker network identifier
/// </summary>
public sealed record NetworkId
{
    /// <summary>
    /// Network ID value
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Initialize a new network ID
    /// </summary>
    /// <param name="value">Network ID value</param>
    private NetworkId(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Create a new network ID from string value
    /// </summary>
    /// <param name="value">Network ID string</param>
    /// <returns>Network ID instance</returns>
    /// <exception cref="ArgumentException">Thrown when value is invalid</exception>
    public static NetworkId From(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Network ID cannot be null or empty", nameof(value));

        if (value.Length < 12)
            throw new ArgumentException("Network ID must be at least 12 characters long", nameof(value));

        if (value.Length > 64)
            throw new ArgumentException("Network ID cannot be longer than 64 characters", nameof(value));

        if (!IsValidHexString(value))
            throw new ArgumentException("Network ID must contain only hexadecimal characters", nameof(value));

        return new NetworkId(value.ToLowerInvariant());
    }

    /// <summary>
    /// Generate a new unique network ID
    /// </summary>
    /// <returns>New network ID instance</returns>
    public static NetworkId New()
    {
        // Generate a 64-character hexadecimal string similar to Docker network IDs
        var guid1 = Guid.NewGuid().ToString("N");
        var guid2 = Guid.NewGuid().ToString("N");
        var networkIdValue = (guid1 + guid2)[..64];
        
        return new NetworkId(networkIdValue);
    }

    /// <summary>
    /// Get short version of network ID (first 12 characters)
    /// </summary>
    /// <returns>Short network ID</returns>
    public string GetShortId()
    {
        return Value[..12];
    }

    /// <summary>
    /// Validate if string contains only hexadecimal characters
    /// </summary>
    /// <param name="value">String to validate</param>
    /// <returns>True if string is valid hexadecimal</returns>
    private static bool IsValidHexString(string value)
    {
        return value.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    /// <summary>
    /// Implicit conversion from string to NetworkId
    /// </summary>
    /// <param name="value">String value</param>
    public static implicit operator NetworkId(string value) => From(value);

    /// <summary>
    /// Implicit conversion from NetworkId to string
    /// </summary>
    /// <param name="networkId">Network ID</param>
    public static implicit operator string(NetworkId networkId) => networkId.Value;

    /// <summary>
    /// String representation of network ID
    /// </summary>
    /// <returns>Network ID value</returns>
    public override string ToString() => Value;
}
