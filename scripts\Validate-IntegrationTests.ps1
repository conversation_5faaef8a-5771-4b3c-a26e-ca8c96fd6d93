# Validate-IntegrationTests.ps1
# Script para validação completa dos Integration Tests após instalação Docker

param(
    [switch]$Verbose = $false,
    [switch]$StopOnFirstFailure = $false,
    [switch]$GenerateReport = $false
)

Write-Host "🧪 Validação Integration Tests - Auto-Instalador Desktop" -ForegroundColor Cyan
Write-Host "=========================================================" -ForegroundColor Cyan
Write-Host ""

$TestResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    SkippedTests = 0
    DockerTests = @{
        Total = 0
        Passed = 0
        Failed = 0
    }
    ConfigurationTests = @{
        Total = 0
        Passed = 0
        Failed = 0
    }
}

function Write-TestStep($message) {
    Write-Host "🔍 $message" -ForegroundColor White
}

function Write-TestSuccess($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-TestFailure($message) {
    Write-Host "❌ $message" -ForegroundColor Red
}

function Write-TestWarning($message) {
    Write-Host "⚠️  $message" -ForegroundColor Yellow
}

# Pré-validação: Verificar Docker
Write-TestStep "Pré-validação: Verificando Docker..."
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-TestSuccess "Docker CLI disponível: $dockerVersion"
    } else {
        Write-TestFailure "Docker CLI não encontrado. Execute primeiro: DOCKER-INSTALLATION-REQUIRED.md"
        exit 1
    }
} catch {
    Write-TestFailure "Docker CLI não encontrado. Execute primeiro: DOCKER-INSTALLATION-REQUIRED.md"
    exit 1
}

try {
    $dockerInfo = docker info --format "{{.ServerVersion}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-TestSuccess "Docker Daemon ativo: versão $dockerInfo"
    } else {
        Write-TestFailure "Docker Daemon não está executando. Inicie Docker Desktop."
        exit 1
    }
} catch {
    Write-TestFailure "Docker Daemon não está executando. Inicie Docker Desktop."
    exit 1
}

# Preparação: Baixar imagens necessárias
Write-TestStep "Preparação: Verificando imagens necessárias..."
$requiredImages = @("nginx:alpine", "redis:alpine")
foreach ($image in $requiredImages) {
    $imageExists = docker images --format "{{.Repository}}:{{.Tag}}" | Select-String -Pattern "^$image$"
    if (-not $imageExists) {
        Write-TestStep "Baixando imagem: $image"
        docker pull $image | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-TestSuccess "Imagem baixada: $image"
        } else {
            Write-TestFailure "Falha ao baixar imagem: $image"
        }
    } else {
        Write-TestSuccess "Imagem disponível: $image"
    }
}

# Execução dos testes
Write-TestStep "Executando Integration Tests..."
Write-Host ""

$testCommand = "dotnet test tests/AutoInstaller.Integration.Tests --logger trx --results-directory TestResults"
if ($Verbose) {
    $testCommand += " --verbosity normal"
} else {
    $testCommand += " --verbosity minimal"
}

if ($StopOnFirstFailure) {
    $testCommand += " --maxcpucount:1"
}

Write-Host "Comando: $testCommand" -ForegroundColor Gray
Write-Host ""

try {
    $testOutput = Invoke-Expression $testCommand 2>&1
    $testExitCode = $LASTEXITCODE
    
    if ($Verbose) {
        Write-Host $testOutput
    }
    
    # Analisar resultados
    $testSummaryLine = $testOutput | Where-Object { $_ -match "Total de testes:|Total tests:" } | Select-Object -Last 1
    $passedLine = $testOutput | Where-Object { $_ -match "Aprovados:|Passed:" } | Select-Object -Last 1  
    $failedLine = $testOutput | Where-Object { $_ -match "Com falha:|Failed:" } | Select-Object -Last 1
    $skippedLine = $testOutput | Where-Object { $_ -match "Ignorados:|Skipped:" } | Select-Object -Last 1
    
    # Extrair números dos resultados
    if ($testSummaryLine -match "(\d+)") {
        $TestResults.TotalTests = [int]$matches[1]
    }
    if ($passedLine -match "(\d+)") {
        $TestResults.PassedTests = [int]$matches[1]
    }
    if ($failedLine -match "(\d+)") {
        $TestResults.FailedTests = [int]$matches[1]
    }
    if ($skippedLine -match "(\d+)") {
        $TestResults.SkippedTests = [int]$matches[1]
    }
    
} catch {
    Write-TestFailure "Erro ao executar testes: $($_.Exception.Message)"
    exit 1
}

# Análise detalhada por categoria
Write-TestStep "Analisando resultados por categoria..."

# Executar testes Docker específicos
try {
    $dockerTestOutput = dotnet test tests/AutoInstaller.Integration.Tests --filter "FullyQualifiedName~DockerServiceIntegrationTests" --verbosity minimal 2>&1
    $dockerTestLine = $dockerTestOutput | Where-Object { $_ -match "Total de testes:|Total tests:" } | Select-Object -Last 1
    $dockerPassedLine = $dockerTestOutput | Where-Object { $_ -match "Aprovados:|Passed:" } | Select-Object -Last 1
    
    if ($dockerTestLine -match "(\d+)") {
        $TestResults.DockerTests.Total = [int]$matches[1]
    }
    if ($dockerPassedLine -match "(\d+)") {
        $TestResults.DockerTests.Passed = [int]$matches[1]
    }
    $TestResults.DockerTests.Failed = $TestResults.DockerTests.Total - $TestResults.DockerTests.Passed
} catch {
    Write-TestWarning "Não foi possível analisar testes Docker específicos"
}

# Executar testes Configuration específicos  
try {
    $configTestOutput = dotnet test tests/AutoInstaller.Integration.Tests --filter "FullyQualifiedName~ConfigurationIntegrationTests" --verbosity minimal 2>&1
    $configTestLine = $configTestOutput | Where-Object { $_ -match "Total de testes:|Total tests:" } | Select-Object -Last 1
    $configPassedLine = $configTestOutput | Where-Object { $_ -match "Aprovados:|Passed:" } | Select-Object -Last 1
    
    if ($configTestLine -match "(\d+)") {
        $TestResults.ConfigurationTests.Total = [int]$matches[1]
    }
    if ($configPassedLine -match "(\d+)") {
        $TestResults.ConfigurationTests.Passed = [int]$matches[1]
    }
    $TestResults.ConfigurationTests.Failed = $TestResults.ConfigurationTests.Total - $TestResults.ConfigurationTests.Passed
} catch {
    Write-TestWarning "Não foi possível analisar testes Configuration específicos"
}

# Relatório final
Write-Host ""
Write-Host "📊 Relatório de Validação" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📈 Resumo Geral:" -ForegroundColor White
Write-Host "   Total de testes: $($TestResults.TotalTests)" -ForegroundColor Gray
Write-Host "   Aprovados: $($TestResults.PassedTests)" -ForegroundColor Green
Write-Host "   Falharam: $($TestResults.FailedTests)" -ForegroundColor $(if ($TestResults.FailedTests -eq 0) { "Green" } else { "Red" })
Write-Host "   Ignorados: $($TestResults.SkippedTests)" -ForegroundColor Yellow
Write-Host ""

Write-Host "🐳 Testes Docker:" -ForegroundColor White
Write-Host "   Total: $($TestResults.DockerTests.Total)" -ForegroundColor Gray
Write-Host "   Aprovados: $($TestResults.DockerTests.Passed)" -ForegroundColor Green
Write-Host "   Falharam: $($TestResults.DockerTests.Failed)" -ForegroundColor $(if ($TestResults.DockerTests.Failed -eq 0) { "Green" } else { "Red" })
Write-Host ""

Write-Host "⚙️  Testes Configuration:" -ForegroundColor White
Write-Host "   Total: $($TestResults.ConfigurationTests.Total)" -ForegroundColor Gray
Write-Host "   Aprovados: $($TestResults.ConfigurationTests.Passed)" -ForegroundColor Green
Write-Host "   Falharam: $($TestResults.ConfigurationTests.Failed)" -ForegroundColor $(if ($TestResults.ConfigurationTests.Failed -eq 0) { "Green" } else { "Red" })
Write-Host ""

# Avaliação final
$successRate = if ($TestResults.TotalTests -gt 0) { 
    [math]::Round(($TestResults.PassedTests / $TestResults.TotalTests) * 100, 1) 
} else { 0 }

Write-Host "🎯 Avaliação Final:" -ForegroundColor White
Write-Host "   Taxa de sucesso: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 90) { "Yellow" } else { "Red" })

if ($TestResults.FailedTests -eq 0) {
    Write-Host ""
    Write-Host "🎉 SUCESSO COMPLETO!" -ForegroundColor Green
    Write-Host "   ✅ Docker configurado corretamente" -ForegroundColor Green
    Write-Host "   ✅ Todos os integration tests passando" -ForegroundColor Green
    Write-Host "   ✅ Auto-Instalador Desktop totalmente funcional" -ForegroundColor Green
    Write-Host "   ✅ Pronto para próximas fases (UI Tests, Performance Tests)" -ForegroundColor Green
} elseif ($TestResults.FailedTests -le 2) {
    Write-Host ""
    Write-Host "⚠️  SUCESSO PARCIAL" -ForegroundColor Yellow
    Write-Host "   ✅ Docker configurado" -ForegroundColor Green
    Write-Host "   ⚠️  Algumas falhas menores detectadas" -ForegroundColor Yellow
    Write-Host "   📋 Revisar logs para detalhes" -ForegroundColor Gray
} else {
    Write-Host ""
    Write-Host "❌ FALHAS DETECTADAS" -ForegroundColor Red
    Write-Host "   ❌ Múltiplas falhas nos integration tests" -ForegroundColor Red
    Write-Host "   🔧 Verificar configuração Docker" -ForegroundColor Yellow
    Write-Host "   📋 Executar: .\scripts\Verify-DockerEnvironment.ps1 -Detailed" -ForegroundColor Gray
}

# Gerar relatório se solicitado
if ($GenerateReport) {
    $reportPath = "TestResults/integration-tests-report.json"
    $TestResults | ConvertTo-Json -Depth 3 | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host ""
    Write-Host "📄 Relatório salvo: $reportPath" -ForegroundColor Blue
}

Write-Host ""
Write-Host "📚 Recursos:" -ForegroundColor Cyan
Write-Host "   • Guia Docker: docs/Docker-Setup-Guide.md" -ForegroundColor Gray
Write-Host "   • Verificar ambiente: .\scripts\Verify-DockerEnvironment.ps1" -ForegroundColor Gray
Write-Host "   • Executar com detalhes: .\scripts\Validate-IntegrationTests.ps1 -Verbose" -ForegroundColor Gray

# Código de saída
if ($TestResults.FailedTests -eq 0) {
    exit 0
} else {
    exit 1
}
