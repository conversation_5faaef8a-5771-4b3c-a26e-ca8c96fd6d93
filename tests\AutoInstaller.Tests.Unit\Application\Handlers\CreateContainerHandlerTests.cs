using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using MediatR;
using AutoInstaller.Application.Handlers;
using AutoInstaller.Application.Commands;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces.Repositories;
using AutoInstaller.Core.Interfaces.Services;
using AutoInstaller.Core.ValueObjects;
using ValidationResult = AutoInstaller.Core.Interfaces.Services.ValidationResult;

namespace AutoInstaller.Tests.Unit.Application.Handlers;

/// <summary>
/// Unit tests for CreateContainerHandler
/// </summary>
public class CreateContainerHandlerTests : IDisposable
{
    private readonly Mock<IContainerRepository> _mockContainerRepository;
    private readonly Mock<IDockerService> _mockDockerService;
    private readonly Mock<ILogger<CreateContainerHandler>> _mockLogger;
    private readonly Mock<IMediator> _mockMediator;
    private readonly CreateContainerHandler _handler;

    public CreateContainerHandlerTests()
    {
        _mockContainerRepository = new Mock<IContainerRepository>();
        _mockDockerService = new Mock<IDockerService>();
        _mockLogger = new Mock<ILogger<CreateContainerHandler>>();
        _mockMediator = new Mock<IMediator>();

        _handler = new CreateContainerHandler(
            _mockContainerRepository.Object,
            _mockDockerService.Object,
            _mockLogger.Object,
            _mockMediator.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateContainer()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            Description = "Test container"
        };

        _mockContainerRepository
            .Setup(x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockDockerService
            .Setup(x => x.ValidateContainerConfigurationAsync(
                It.IsAny<string>(),
                It.IsAny<ImageTag>(),
                It.IsAny<IEnumerable<PortMapping>>(),
                It.IsAny<IEnumerable<VolumeMount>>(),
                It.IsAny<IEnumerable<EnvironmentVariable>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationResult.Success());

        _mockContainerRepository
            .Setup(x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Name.Should().Be(command.Name);
        result.ImageTag.Should().Be(command.ImageTag);
        result.IsStarted.Should().BeFalse();
        result.ErrorMessage.Should().BeNull();

        _mockContainerRepository.Verify(
            x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _mockContainerRepository.Verify(
            x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithUnavailableName_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "existing-container",
            ImageTag = "nginx:latest"
        };

        _mockContainerRepository
            .Setup(x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Name.Should().Be(command.Name);
        result.ContainerId.Should().BeEmpty();
        result.Status.Should().Be("Failed");
        result.ErrorMessage.Should().Contain("already in use");

        _mockContainerRepository.Verify(
            x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _mockContainerRepository.Verify(
            x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task Handle_WithInvalidConfiguration_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest"
        };

        _mockContainerRepository
            .Setup(x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockDockerService
            .Setup(x => x.ValidateContainerConfigurationAsync(
                It.IsAny<string>(),
                It.IsAny<ImageTag>(),
                It.IsAny<IEnumerable<PortMapping>>(),
                It.IsAny<IEnumerable<VolumeMount>>(),
                It.IsAny<IEnumerable<EnvironmentVariable>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationResult.Failure("Invalid port mapping", "Invalid volume mount"));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Name.Should().Be(command.Name);
        result.ContainerId.Should().BeEmpty();
        result.Status.Should().Be("Failed");
        result.ErrorMessage.Should().Contain("Configuration validation failed");
        result.ErrorMessage.Should().Contain("Invalid port mapping");
        result.ErrorMessage.Should().Contain("Invalid volume mount");

        _mockContainerRepository.Verify(
            x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task Handle_WithStartImmediately_ShouldStartContainer()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            StartImmediately = true
        };

        _mockContainerRepository
            .Setup(x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockDockerService
            .Setup(x => x.ValidateContainerConfigurationAsync(
                It.IsAny<string>(),
                It.IsAny<ImageTag>(),
                It.IsAny<IEnumerable<PortMapping>>(),
                It.IsAny<IEnumerable<VolumeMount>>(),
                It.IsAny<IEnumerable<EnvironmentVariable>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationResult.Success());

        _mockContainerRepository
            .Setup(x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockContainerRepository
            .Setup(x => x.UpdateAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Name.Should().Be(command.Name);
        result.IsStarted.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();

        _mockContainerRepository.Verify(
            x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _mockContainerRepository.Verify(
            x => x.UpdateAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithComplexConfiguration_ShouldProcessAllComponents()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "complex-container",
            ImageTag = "nginx:latest",
            Description = "Complex test container",
            PortMappings = new List<CreatePortMappingDto>
            {
                new() { HostPort = 8080, ContainerPort = 80, Protocol = "TCP" },
                new() { HostPort = 8443, ContainerPort = 443, Protocol = "TCP" }
            },
            VolumeMounts = new List<CreateVolumeMountDto>
            {
                new() { Source = "/host/data", ContainerPath = "/data", Type = "bind" },
                new() { Source = "config-volume", ContainerPath = "/config", Type = "volume" }
            },
            EnvironmentVariables = new List<CreateEnvironmentVariableDto>
            {
                new() { Name = "NODE_ENV", Value = "production" },
                new() { Name = "DATABASE_URL", Value = "secret-url", IsSensitive = true }
            },
            Networks = new List<string> { "bridge", "custom-network" }
        };

        _mockContainerRepository
            .Setup(x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockDockerService
            .Setup(x => x.ValidateContainerConfigurationAsync(
                It.IsAny<string>(),
                It.IsAny<ImageTag>(),
                It.IsAny<IEnumerable<PortMapping>>(),
                It.IsAny<IEnumerable<VolumeMount>>(),
                It.IsAny<IEnumerable<EnvironmentVariable>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationResult.Success());

        _mockContainerRepository
            .Setup(x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Name.Should().Be(command.Name);
        result.ImageTag.Should().Be(command.ImageTag);

        // Verify that validation was called with correct parameters
        _mockDockerService.Verify(
            x => x.ValidateContainerConfigurationAsync(
                command.Name,
                It.IsAny<ImageTag>(),
                It.Is<IEnumerable<PortMapping>>(pm => pm.Count() == 2),
                It.Is<IEnumerable<VolumeMount>>(vm => vm.Count() == 2),
                It.Is<IEnumerable<EnvironmentVariable>>(ev => ev.Count() == 2),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithException_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest"
        };

        _mockContainerRepository
            .Setup(x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Name.Should().Be(command.Name);
        result.ContainerId.Should().BeEmpty();
        result.Status.Should().Be("Failed");
        result.ErrorMessage.Should().Be("Database connection failed");
    }

    [Fact]
    public async Task Handle_ShouldPublishDomainEvents()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest"
        };

        _mockContainerRepository
            .Setup(x => x.IsNameAvailableAsync(command.Name, It.IsAny<ContainerId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockDockerService
            .Setup(x => x.ValidateContainerConfigurationAsync(
                It.IsAny<string>(),
                It.IsAny<ImageTag>(),
                It.IsAny<IEnumerable<PortMapping>>(),
                It.IsAny<IEnumerable<VolumeMount>>(),
                It.IsAny<IEnumerable<EnvironmentVariable>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationResult.Success());

        _mockContainerRepository
            .Setup(x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();

        // Verify that domain events were published
        _mockMediator.Verify(
            x => x.Publish(It.IsAny<INotification>(), It.IsAny<CancellationToken>()),
            Times.AtLeastOnce);
    }

    public void Dispose()
    {
        // Clean up if needed
    }
}
