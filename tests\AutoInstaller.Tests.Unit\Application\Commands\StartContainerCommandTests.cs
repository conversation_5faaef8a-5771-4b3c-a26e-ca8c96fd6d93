using Xunit;
using FluentAssertions;
using AutoInstaller.Application.Commands;

namespace AutoInstaller.Tests.Unit.Application.Commands;

/// <summary>
/// Unit tests for StartContainerCommand and StopContainerCommand
/// </summary>
public class StartContainerCommandTests
{
    [Fact]
    public void StartContainerCommand_WithValidContainerIdOrName_ShouldCreateCommand()
    {
        // Arrange
        var containerIdOrName = "test-container";

        // Act
        var command = new StartContainerCommand { ContainerIdOrName = containerIdOrName };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
    }

    [Theory]
    [InlineData("container-id-123")]
    [InlineData("my-nginx-container")]
    [InlineData("a1b2c3d4e5f6")]
    public void StartContainerCommand_WithVariousValidNames_ShouldCreateCommand(string containerIdOrName)
    {
        // Act
        var command = new StartContainerCommand { ContainerIdOrName = containerIdOrName };

        // Assert
        command.ContainerIdOrName.Should().Be(containerIdOrName);
    }

    [Fact]
    public void StopContainerCommand_WithValidContainerIdOrName_ShouldCreateCommand()
    {
        // Arrange
        var containerIdOrName = "test-container";

        // Act
        var command = new StopContainerCommand { ContainerIdOrName = containerIdOrName };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
        command.TimeoutSeconds.Should().Be(10); // Default value
    }

    [Fact]
    public void StopContainerCommand_WithCustomTimeout_ShouldSetTimeout()
    {
        // Arrange
        var containerIdOrName = "test-container";
        var customTimeout = 30;

        // Act
        var command = new StopContainerCommand 
        { 
            ContainerIdOrName = containerIdOrName,
            TimeoutSeconds = customTimeout
        };

        // Assert
        command.ContainerIdOrName.Should().Be(containerIdOrName);
        command.TimeoutSeconds.Should().Be(customTimeout);
    }

    [Theory]
    [InlineData(5)]
    [InlineData(15)]
    [InlineData(60)]
    public void StopContainerCommand_WithVariousTimeouts_ShouldSetCorrectTimeout(int timeout)
    {
        // Arrange
        var containerIdOrName = "test-container";

        // Act
        var command = new StopContainerCommand 
        { 
            ContainerIdOrName = containerIdOrName,
            TimeoutSeconds = timeout
        };

        // Assert
        command.TimeoutSeconds.Should().Be(timeout);
    }

    [Fact]
    public void StartContainerCommand_Equality_WithSameValues_ShouldBeEqual()
    {
        // Arrange
        var containerIdOrName = "test-container";
        var command1 = new StartContainerCommand { ContainerIdOrName = containerIdOrName };
        var command2 = new StartContainerCommand { ContainerIdOrName = containerIdOrName };

        // Act & Assert
        command1.Should().Be(command2);
        command1.GetHashCode().Should().Be(command2.GetHashCode());
    }

    [Fact]
    public void StartContainerCommand_Equality_WithDifferentValues_ShouldNotBeEqual()
    {
        // Arrange
        var command1 = new StartContainerCommand { ContainerIdOrName = "container1" };
        var command2 = new StartContainerCommand { ContainerIdOrName = "container2" };

        // Act & Assert
        command1.Should().NotBe(command2);
    }

    [Fact]
    public void StopContainerCommand_Equality_WithSameValues_ShouldBeEqual()
    {
        // Arrange
        var containerIdOrName = "test-container";
        var timeout = 15;
        var command1 = new StopContainerCommand 
        { 
            ContainerIdOrName = containerIdOrName,
            TimeoutSeconds = timeout
        };
        var command2 = new StopContainerCommand 
        { 
            ContainerIdOrName = containerIdOrName,
            TimeoutSeconds = timeout
        };

        // Act & Assert
        command1.Should().Be(command2);
        command1.GetHashCode().Should().Be(command2.GetHashCode());
    }

    [Fact]
    public void StopContainerCommand_Equality_WithDifferentTimeout_ShouldNotBeEqual()
    {
        // Arrange
        var containerIdOrName = "test-container";
        var command1 = new StopContainerCommand 
        { 
            ContainerIdOrName = containerIdOrName,
            TimeoutSeconds = 10
        };
        var command2 = new StopContainerCommand 
        { 
            ContainerIdOrName = containerIdOrName,
            TimeoutSeconds = 20
        };

        // Act & Assert
        command1.Should().NotBe(command2);
    }

    [Fact]
    public void Commands_ToString_ShouldReturnMeaningfulString()
    {
        // Arrange
        var startCommand = new StartContainerCommand { ContainerIdOrName = "test-container" };
        var stopCommand = new StopContainerCommand
        {
            ContainerIdOrName = "test-container",
            TimeoutSeconds = 15
        };

        // Act
        var startString = startCommand.ToString();
        var stopString = stopCommand.ToString();

        // Assert
        startString.Should().Contain("test-container");
        stopString.Should().Contain("test-container");
        stopString.Should().Contain("15");
    }

    [Fact]
    public void RemoveContainerCommand_WithValidParameters_ShouldCreateCommand()
    {
        // Arrange
        var containerIdOrName = "test-container";
        var force = true;
        var removeVolumes = true;

        // Act
        var command = new RemoveContainerCommand
        {
            ContainerIdOrName = containerIdOrName,
            Force = force,
            RemoveVolumes = removeVolumes
        };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
        command.Force.Should().Be(force);
        command.RemoveVolumes.Should().Be(removeVolumes);
    }

    [Fact]
    public void RemoveContainerCommand_WithDefaultValues_ShouldUseDefaults()
    {
        // Arrange
        var containerIdOrName = "test-container";

        // Act
        var command = new RemoveContainerCommand
        {
            ContainerIdOrName = containerIdOrName
        };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
        command.Force.Should().BeFalse(); // Default value
        command.RemoveVolumes.Should().BeFalse(); // Default value
    }

    [Fact]
    public void RestartContainerCommand_WithValidParameters_ShouldCreateCommand()
    {
        // Arrange
        var containerIdOrName = "test-container";
        var timeoutSeconds = 15;

        // Act
        var command = new RestartContainerCommand
        {
            ContainerIdOrName = containerIdOrName,
            TimeoutSeconds = timeoutSeconds
        };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
        command.TimeoutSeconds.Should().Be(timeoutSeconds);
    }

    [Fact]
    public void RestartContainerCommand_WithDefaultTimeout_ShouldUseDefaultValue()
    {
        // Arrange
        var containerIdOrName = "test-container";

        // Act
        var command = new RestartContainerCommand
        {
            ContainerIdOrName = containerIdOrName
        };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
        command.TimeoutSeconds.Should().Be(10); // Default value
    }

    [Fact]
    public void PauseContainerCommand_WithValidContainerIdOrName_ShouldCreateCommand()
    {
        // Arrange
        var containerIdOrName = "test-container";

        // Act
        var command = new PauseContainerCommand
        {
            ContainerIdOrName = containerIdOrName
        };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
    }

    [Fact]
    public void UnpauseContainerCommand_WithValidContainerIdOrName_ShouldCreateCommand()
    {
        // Arrange
        var containerIdOrName = "test-container";

        // Act
        var command = new UnpauseContainerCommand
        {
            ContainerIdOrName = containerIdOrName
        };

        // Assert
        command.Should().NotBeNull();
        command.ContainerIdOrName.Should().Be(containerIdOrName);
    }

    [Fact]
    public void ContainerOperationResult_WithValidParameters_ShouldCreateResult()
    {
        // Arrange
        var containerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var name = "test-container";
        var operation = "start";
        var status = "running";
        var success = true;
        var timestamp = DateTime.UtcNow;

        // Act
        var result = new ContainerOperationResult
        {
            ContainerId = containerId,
            Name = name,
            Operation = operation,
            Status = status,
            Success = success,
            Timestamp = timestamp
        };

        // Assert
        result.Should().NotBeNull();
        result.ContainerId.Should().Be(containerId);
        result.Name.Should().Be(name);
        result.Operation.Should().Be(operation);
        result.Status.Should().Be(status);
        result.Success.Should().Be(success);
        result.ErrorMessage.Should().BeNull();
        result.Timestamp.Should().Be(timestamp);
    }

    [Fact]
    public void ContainerOperationResult_WithErrorMessage_ShouldIncludeError()
    {
        // Arrange
        var containerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var name = "test-container";
        var operation = "start";
        var status = "failed";
        var success = false;
        var errorMessage = "Container not found";
        var timestamp = DateTime.UtcNow;

        // Act
        var result = new ContainerOperationResult
        {
            ContainerId = containerId,
            Name = name,
            Operation = operation,
            Status = status,
            Success = success,
            ErrorMessage = errorMessage,
            Timestamp = timestamp
        };

        // Assert
        result.Should().NotBeNull();
        result.ContainerId.Should().Be(containerId);
        result.Name.Should().Be(name);
        result.Operation.Should().Be(operation);
        result.Status.Should().Be(status);
        result.Success.Should().Be(success);
        result.ErrorMessage.Should().Be(errorMessage);
        result.Timestamp.Should().Be(timestamp);
    }

    [Fact]
    public void AllContainerCommands_ShouldImplementIRequest()
    {
        // Assert
        typeof(StartContainerCommand).Should().Implement<MediatR.IRequest<ContainerOperationResult>>();
        typeof(StopContainerCommand).Should().Implement<MediatR.IRequest<ContainerOperationResult>>();
        typeof(RemoveContainerCommand).Should().Implement<MediatR.IRequest<ContainerOperationResult>>();
        typeof(RestartContainerCommand).Should().Implement<MediatR.IRequest<ContainerOperationResult>>();
        typeof(PauseContainerCommand).Should().Implement<MediatR.IRequest<ContainerOperationResult>>();
        typeof(UnpauseContainerCommand).Should().Implement<MediatR.IRequest<ContainerOperationResult>>();
    }

    [Fact]
    public void AllContainerCommands_ShouldBeRecords()
    {
        // Assert - Records have specific characteristics
        typeof(StartContainerCommand).Should().BeSealed();
        typeof(StopContainerCommand).Should().BeSealed();
        typeof(RemoveContainerCommand).Should().BeSealed();
        typeof(RestartContainerCommand).Should().BeSealed();
        typeof(PauseContainerCommand).Should().BeSealed();
        typeof(UnpauseContainerCommand).Should().BeSealed();
        typeof(ContainerOperationResult).Should().BeSealed();
    }
}
