{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "Docker.DotNet": "Debug", "AutoInstaller": "Verbose"}}}, "Application": {"Environment": "Development", "EnablePlugins": true, "MaxConcurrentOperations": 3, "CacheExpirationMinutes": 5}, "UI": {"EnableAnimations": false, "RefreshInterval": 2000, "PageSize": 10}, "Plugins": {"EnableAutoLoad": true, "EnableHotReload": true, "IsolationLevel": "AppDomain", "MaxPluginMemoryMB": 128, "PluginTimeout": 10}, "Docker": {"Timeout": 10, "HealthCheckInterval": "00:00:10", "EnableHealthChecks": true}, "Performance": {"EnableCaching": false, "CacheSize": 50, "EnableCompression": false, "MaxThreads": 5}}