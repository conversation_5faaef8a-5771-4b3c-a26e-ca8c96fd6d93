using Xunit;
using FluentAssertions;
using AutoInstaller.Application.Commands;

namespace AutoInstaller.Tests.Unit.Application.Commands;

/// <summary>
/// Unit tests for CreateContainerCommand and related DTOs
/// </summary>
public class CreateContainerCommandTests
{
    [Fact]
    public void CreateContainerCommand_WithMinimalParameters_ShouldCreateCommand()
    {
        // Arrange
        var name = "test-container";
        var imageTag = "nginx:latest";

        // Act
        var command = new CreateContainerCommand
        {
            Name = name,
            ImageTag = imageTag
        };

        // Assert
        command.Should().NotBeNull();
        command.Name.Should().Be(name);
        command.ImageTag.Should().Be(imageTag);
        command.Description.Should().BeNull();
        command.PortMappings.Should().BeEmpty();
        command.VolumeMounts.Should().BeEmpty();
        command.EnvironmentVariables.Should().BeEmpty();
        command.Networks.Should().BeEmpty();
        command.StartImmediately.Should().BeFalse();
    }

    [Fact]
    public void CreateContainerCommand_WithAllParameters_ShouldCreateCommand()
    {
        // Arrange
        var name = "test-container";
        var imageTag = "nginx:latest";
        var description = "Test container description";
        var portMappings = new List<CreatePortMappingDto>
        {
            new() { HostPort = 8080, ContainerPort = 80, Protocol = "TCP" }
        };
        var volumeMounts = new List<CreateVolumeMountDto>
        {
            new() { Source = "/host/path", ContainerPath = "/container/path", Type = "bind" }
        };
        var environmentVariables = new List<CreateEnvironmentVariableDto>
        {
            new() { Name = "ENV_VAR", Value = "test-value" }
        };
        var networks = new List<string> { "bridge", "custom-network" };
        var startImmediately = true;

        // Act
        var command = new CreateContainerCommand
        {
            Name = name,
            ImageTag = imageTag,
            Description = description,
            PortMappings = portMappings,
            VolumeMounts = volumeMounts,
            EnvironmentVariables = environmentVariables,
            Networks = networks,
            StartImmediately = startImmediately
        };

        // Assert
        command.Should().NotBeNull();
        command.Name.Should().Be(name);
        command.ImageTag.Should().Be(imageTag);
        command.Description.Should().Be(description);
        command.PortMappings.Should().HaveCount(1);
        command.VolumeMounts.Should().HaveCount(1);
        command.EnvironmentVariables.Should().HaveCount(1);
        command.Networks.Should().HaveCount(2);
        command.StartImmediately.Should().BeTrue();
    }

    [Fact]
    public void CreatePortMappingDto_WithValidParameters_ShouldCreateDto()
    {
        // Arrange
        var hostPort = 8080;
        var containerPort = 80;
        var protocol = "TCP";
        var hostIP = "127.0.0.1";

        // Act
        var dto = new CreatePortMappingDto
        {
            HostPort = hostPort,
            ContainerPort = containerPort,
            Protocol = protocol,
            HostIP = hostIP
        };

        // Assert
        dto.Should().NotBeNull();
        dto.HostPort.Should().Be(hostPort);
        dto.ContainerPort.Should().Be(containerPort);
        dto.Protocol.Should().Be(protocol);
        dto.HostIP.Should().Be(hostIP);
    }

    [Fact]
    public void CreatePortMappingDto_WithDefaultProtocol_ShouldUseTCP()
    {
        // Arrange
        var hostPort = 8080;
        var containerPort = 80;

        // Act
        var dto = new CreatePortMappingDto
        {
            HostPort = hostPort,
            ContainerPort = containerPort
        };

        // Assert
        dto.Should().NotBeNull();
        dto.HostPort.Should().Be(hostPort);
        dto.ContainerPort.Should().Be(containerPort);
        dto.Protocol.Should().Be("TCP"); // Default value
        dto.HostIP.Should().BeNull();
    }

    [Theory]
    [InlineData("TCP")]
    [InlineData("UDP")]
    [InlineData("SCTP")]
    public void CreatePortMappingDto_WithDifferentProtocols_ShouldSetProtocol(string protocol)
    {
        // Arrange
        var hostPort = 8080;
        var containerPort = 80;

        // Act
        var dto = new CreatePortMappingDto
        {
            HostPort = hostPort,
            ContainerPort = containerPort,
            Protocol = protocol
        };

        // Assert
        dto.Protocol.Should().Be(protocol);
    }

    [Fact]
    public void CreateVolumeMountDto_WithValidParameters_ShouldCreateDto()
    {
        // Arrange
        var source = "/host/path";
        var containerPath = "/container/path";
        var type = "bind";
        var isReadOnly = true;

        // Act
        var dto = new CreateVolumeMountDto
        {
            Source = source,
            ContainerPath = containerPath,
            Type = type,
            IsReadOnly = isReadOnly
        };

        // Assert
        dto.Should().NotBeNull();
        dto.Source.Should().Be(source);
        dto.ContainerPath.Should().Be(containerPath);
        dto.Type.Should().Be(type);
        dto.IsReadOnly.Should().Be(isReadOnly);
    }

    [Fact]
    public void CreateVolumeMountDto_WithDefaultValues_ShouldUseDefaults()
    {
        // Arrange
        var source = "my-volume";
        var containerPath = "/data";

        // Act
        var dto = new CreateVolumeMountDto
        {
            Source = source,
            ContainerPath = containerPath
        };

        // Assert
        dto.Should().NotBeNull();
        dto.Source.Should().Be(source);
        dto.ContainerPath.Should().Be(containerPath);
        dto.Type.Should().Be("volume"); // Default value
        dto.IsReadOnly.Should().BeFalse(); // Default value
    }

    [Theory]
    [InlineData("bind")]
    [InlineData("volume")]
    [InlineData("tmpfs")]
    public void CreateVolumeMountDto_WithDifferentTypes_ShouldSetType(string type)
    {
        // Arrange
        var source = "test-source";
        var containerPath = "/test";

        // Act
        var dto = new CreateVolumeMountDto
        {
            Source = source,
            ContainerPath = containerPath,
            Type = type
        };

        // Assert
        dto.Type.Should().Be(type);
    }

    [Fact]
    public void CreateEnvironmentVariableDto_WithValidParameters_ShouldCreateDto()
    {
        // Arrange
        var name = "DATABASE_URL";
        var value = "postgresql://localhost:5432/mydb";
        var isSensitive = true;

        // Act
        var dto = new CreateEnvironmentVariableDto
        {
            Name = name,
            Value = value,
            IsSensitive = isSensitive
        };

        // Assert
        dto.Should().NotBeNull();
        dto.Name.Should().Be(name);
        dto.Value.Should().Be(value);
        dto.IsSensitive.Should().Be(isSensitive);
    }

    [Fact]
    public void CreateEnvironmentVariableDto_WithoutSensitiveFlag_ShouldBeNull()
    {
        // Arrange
        var name = "NODE_ENV";
        var value = "production";

        // Act
        var dto = new CreateEnvironmentVariableDto
        {
            Name = name,
            Value = value
        };

        // Assert
        dto.Should().NotBeNull();
        dto.Name.Should().Be(name);
        dto.Value.Should().Be(value);
        dto.IsSensitive.Should().BeNull(); // Default value
    }

    [Fact]
    public void CreateContainerResult_WithValidParameters_ShouldCreateResult()
    {
        // Arrange
        var containerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var name = "test-container";
        var imageTag = "nginx:latest";
        var status = "created";
        var isStarted = false;
        var createdAt = DateTime.UtcNow;
        var success = true;

        // Act
        var result = new CreateContainerResult
        {
            ContainerId = containerId,
            Name = name,
            ImageTag = imageTag,
            Status = status,
            IsStarted = isStarted,
            CreatedAt = createdAt,
            Success = success
        };

        // Assert
        result.Should().NotBeNull();
        result.ContainerId.Should().Be(containerId);
        result.Name.Should().Be(name);
        result.ImageTag.Should().Be(imageTag);
        result.Status.Should().Be(status);
        result.IsStarted.Should().Be(isStarted);
        result.CreatedAt.Should().Be(createdAt);
        result.Success.Should().Be(success);
        result.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public void CreateContainerResult_WithError_ShouldIncludeErrorMessage()
    {
        // Arrange
        var containerId = "";
        var name = "test-container";
        var imageTag = "nginx:latest";
        var status = "failed";
        var isStarted = false;
        var createdAt = DateTime.UtcNow;
        var success = false;
        var errorMessage = "Image not found";

        // Act
        var result = new CreateContainerResult
        {
            ContainerId = containerId,
            Name = name,
            ImageTag = imageTag,
            Status = status,
            IsStarted = isStarted,
            CreatedAt = createdAt,
            Success = success,
            ErrorMessage = errorMessage
        };

        // Assert
        result.Should().NotBeNull();
        result.ContainerId.Should().Be(containerId);
        result.Name.Should().Be(name);
        result.ImageTag.Should().Be(imageTag);
        result.Status.Should().Be(status);
        result.IsStarted.Should().Be(isStarted);
        result.CreatedAt.Should().Be(createdAt);
        result.Success.Should().Be(success);
        result.ErrorMessage.Should().Be(errorMessage);
    }

    [Fact]
    public void CreateContainerCommand_ShouldImplementIRequest()
    {
        // Assert
        typeof(CreateContainerCommand).Should().Implement<MediatR.IRequest<CreateContainerResult>>();
    }

    [Fact]
    public void CreateContainerCommand_ShouldBeRecord()
    {
        // Assert
        typeof(CreateContainerCommand).Should().BeSealed();
        typeof(CreateContainerResult).Should().BeSealed();
        typeof(CreatePortMappingDto).Should().BeSealed();
        typeof(CreateVolumeMountDto).Should().BeSealed();
        typeof(CreateEnvironmentVariableDto).Should().BeSealed();
    }
}
