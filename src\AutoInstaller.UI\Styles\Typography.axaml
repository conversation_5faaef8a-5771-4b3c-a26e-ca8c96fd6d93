<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Typography styles -->
    <Styles.Resources>
        <!-- Font Families -->
        <FontFamily x:Key="PrimaryFont">Inter</FontFamily>
        <FontFamily x:Key="MonospaceFont">Consolas, Monaco, 'Courier New', monospace</FontFamily>
        
        <!-- Font Sizes -->
        <x:Double x:Key="FontSizeSmall">12</x:Double>
        <x:Double x:Key="FontSizeNormal">14</x:Double>
        <x:Double x:Key="FontSizeMedium">16</x:Double>
        <x:Double x:Key="FontSizeLarge">18</x:Double>
        <x:Double x:Key="FontSizeXLarge">24</x:Double>
        <x:Double x:Key="FontSizeXXLarge">32</x:Double>
    </Styles.Resources>
    
    <!-- Text Block Styles -->
    <Style Selector="TextBlock.Heading1">
        <Setter Property="FontSize" Value="{StaticResource FontSizeXXLarge}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryText}"/>
    </Style>
    
    <Style Selector="TextBlock.Heading2">
        <Setter Property="FontSize" Value="{StaticResource FontSizeXLarge}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryText}"/>
    </Style>
    
    <Style Selector="TextBlock.Heading3">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLarge}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryText}"/>
    </Style>
    
    <Style Selector="TextBlock.Body">
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryText}"/>
    </Style>
    
    <Style Selector="TextBlock.Caption">
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{DynamicResource SecondaryText}"/>
    </Style>
    
    <Style Selector="TextBlock.Code">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFont}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="Background" Value="{DynamicResource TertiaryBackground}"/>
        <Setter Property="Padding" Value="4,2"/>
    </Style>
    
</Styles>
