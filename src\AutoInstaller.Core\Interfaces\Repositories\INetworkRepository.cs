using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Interfaces.Repositories;

/// <summary>
/// Repository interface for Network entity
/// </summary>
public interface INetworkRepository
{
    /// <summary>
    /// Get network by ID
    /// </summary>
    /// <param name="id">Network ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Network if found, null otherwise</returns>
    Task<Network?> GetByIdAsync(NetworkId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get network by name
    /// </summary>
    /// <param name="name">Network name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Network if found, null otherwise</returns>
    Task<Network?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all networks
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all networks</returns>
    Task<IReadOnlyList<Network>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get networks by driver
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of networks with specified driver</returns>
    Task<IReadOnlyList<Network>> GetByDriverAsync(NetworkDriver driver, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get networks by scope
    /// </summary>
    /// <param name="scope">Network scope</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of networks with specified scope</returns>
    Task<IReadOnlyList<Network>> GetByScopeAsync(NetworkScope scope, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get system networks (bridge, host, none)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of system networks</returns>
    Task<IReadOnlyList<Network>> GetSystemNetworksAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user-defined networks
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of user-defined networks</returns>
    Task<IReadOnlyList<Network>> GetUserDefinedAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get networks with connected containers
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of networks that have connected containers</returns>
    Task<IReadOnlyList<Network>> GetWithConnectedContainersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get networks by container
    /// </summary>
    /// <param name="containerId">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of networks connected to the container</returns>
    Task<IReadOnlyList<Network>> GetByContainerAsync(ContainerId containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if network exists by ID
    /// </summary>
    /// <param name="id">Network ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if network exists</returns>
    Task<bool> ExistsAsync(NetworkId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if network name is available
    /// </summary>
    /// <param name="name">Network name</param>
    /// <param name="excludeId">Network ID to exclude from check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if name is available</returns>
    Task<bool> IsNameAvailableAsync(string name, NetworkId? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add new network
    /// </summary>
    /// <param name="network">Network to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task AddAsync(Network network, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing network
    /// </summary>
    /// <param name="network">Network to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateAsync(Network network, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove network
    /// </summary>
    /// <param name="network">Network to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveAsync(Network network, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove network by ID
    /// </summary>
    /// <param name="id">Network ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveByIdAsync(NetworkId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get networks with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of networks</returns>
    Task<(IReadOnlyList<Network> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search networks by name pattern
    /// </summary>
    /// <param name="namePattern">Name pattern to search</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching networks</returns>
    Task<IReadOnlyList<Network>> SearchByNameAsync(string namePattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get network count
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total number of networks</returns>
    Task<int> GetCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get network count by driver
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of networks with specified driver</returns>
    Task<int> GetCountByDriverAsync(NetworkDriver driver, CancellationToken cancellationToken = default);
}
