using Xunit;
using FluentAssertions;
using FluentValidation.TestHelper;
using AutoInstaller.Application.Commands;
using AutoInstaller.Application.Validators;

namespace AutoInstaller.Tests.Unit.Application.Validators;

/// <summary>
/// Unit tests for CreateContainerCommandValidator
/// </summary>
public class CreateContainerCommandValidatorTests
{
    private readonly CreateContainerCommandValidator _validator;

    public CreateContainerCommandValidatorTests()
    {
        _validator = new CreateContainerCommandValidator();
    }

    [Fact]
    public void Validate_WithValidCommand_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            Description = "Test container description"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithEmptyName_ShouldHaveValidationError(string name)
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = name,
            ImageTag = "nginx:latest"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Container name is required");
    }

    [Fact]
    public void Validate_WithTooLongName_ShouldHaveValidationError()
    {
        // Arrange
        var longName = new string('a', 256); // 256 characters
        var command = new CreateContainerCommand
        {
            Name = longName,
            ImageTag = "nginx:latest"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Container name must be between 1 and 255 characters");
    }

    [Theory]
    [InlineData("-invalid-start")]
    [InlineData(".invalid-start")]
    [InlineData("_invalid-start")]
    [InlineData("invalid@name")]
    [InlineData("invalid name")]
    [InlineData("invalid#name")]
    public void Validate_WithInvalidNameFormat_ShouldHaveValidationError(string invalidName)
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = invalidName,
            ImageTag = "nginx:latest"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Container name must start with alphanumeric character and contain only letters, numbers, underscores, periods, and hyphens");
    }

    [Theory]
    [InlineData("valid-name")]
    [InlineData("valid_name")]
    [InlineData("valid.name")]
    [InlineData("ValidName123")]
    [InlineData("a")]
    [InlineData("123container")]
    public void Validate_WithValidNameFormat_ShouldNotHaveValidationError(string validName)
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = validName,
            ImageTag = "nginx:latest"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithEmptyImageTag_ShouldHaveValidationError(string imageTag)
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = imageTag
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ImageTag)
            .WithErrorMessage("Image tag is required");
    }

    [Theory]
    [InlineData("invalid-image-tag")]
    [InlineData("")]
    [InlineData("invalid:")]
    [InlineData(":invalid")]
    public void Validate_WithInvalidImageTag_ShouldHaveValidationError(string invalidImageTag)
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = invalidImageTag
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        if (!string.IsNullOrWhiteSpace(invalidImageTag))
        {
            result.ShouldHaveValidationErrorFor(x => x.ImageTag)
                .WithErrorMessage("Image tag must be in valid format (repository:tag)");
        }
    }

    [Theory]
    [InlineData("nginx:latest")]
    [InlineData("ubuntu:20.04")]
    [InlineData("redis:alpine")]
    [InlineData("postgres:13")]
    public void Validate_WithValidImageTag_ShouldNotHaveValidationError(string validImageTag)
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = validImageTag
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.ImageTag);
    }

    [Fact]
    public void Validate_WithTooLongDescription_ShouldHaveValidationError()
    {
        // Arrange
        var longDescription = new string('a', 1001); // 1001 characters
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            Description = longDescription
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Description)
            .WithErrorMessage("Description cannot exceed 1000 characters");
    }

    [Fact]
    public void Validate_WithValidDescription_ShouldNotHaveValidationError()
    {
        // Arrange
        var validDescription = new string('a', 1000); // Exactly 1000 characters
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            Description = validDescription
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Validate_WithDuplicateHostPorts_ShouldHaveValidationError()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            PortMappings = new List<CreatePortMappingDto>
            {
                new() { HostPort = 8080, ContainerPort = 80, Protocol = "TCP" },
                new() { HostPort = 8080, ContainerPort = 443, Protocol = "TCP" } // Duplicate host port
            }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PortMappings)
            .WithErrorMessage("Host ports must be unique");
    }

    [Fact]
    public void Validate_WithUniqueHostPorts_ShouldNotHaveValidationError()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            PortMappings = new List<CreatePortMappingDto>
            {
                new() { HostPort = 8080, ContainerPort = 80, Protocol = "TCP" },
                new() { HostPort = 8443, ContainerPort = 443, Protocol = "TCP" }
            }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.PortMappings);
    }

    [Fact]
    public void Validate_WithEmptyNetworkName_ShouldHaveValidationError()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            Networks = new List<string> { "bridge", "", "custom-network" }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Networks)
            .WithErrorMessage("Network names cannot be empty");
    }

    [Fact]
    public void Validate_WithValidNetworkNames_ShouldNotHaveValidationError()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "test-container",
            ImageTag = "nginx:latest",
            Networks = new List<string> { "bridge", "custom-network", "host" }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Networks);
    }

    [Fact]
    public void Validate_WithComplexValidCommand_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var command = new CreateContainerCommand
        {
            Name = "complex-container",
            ImageTag = "nginx:latest",
            Description = "A complex test container with all features",
            PortMappings = new List<CreatePortMappingDto>
            {
                new() { HostPort = 8080, ContainerPort = 80, Protocol = "TCP", HostIP = "127.0.0.1" },
                new() { HostPort = 8443, ContainerPort = 443, Protocol = "TCP" }
            },
            VolumeMounts = new List<CreateVolumeMountDto>
            {
                new() { Source = "/host/data", ContainerPath = "/data", Type = "bind", IsReadOnly = false },
                new() { Source = "config-volume", ContainerPath = "/config", Type = "volume", IsReadOnly = true }
            },
            EnvironmentVariables = new List<CreateEnvironmentVariableDto>
            {
                new() { Name = "NODE_ENV", Value = "production" },
                new() { Name = "DATABASE_URL", Value = "postgresql://localhost:5432/db", IsSensitive = true }
            },
            Networks = new List<string> { "bridge", "custom-network" },
            StartImmediately = true
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }
}

/// <summary>
/// Unit tests for CreatePortMappingDtoValidator
/// </summary>
public class CreatePortMappingDtoValidatorTests
{
    private readonly CreatePortMappingDtoValidator _validator;

    public CreatePortMappingDtoValidatorTests()
    {
        _validator = new CreatePortMappingDtoValidator();
    }

    [Fact]
    public void Validate_WithValidPortMapping_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = 80,
            Protocol = "TCP",
            HostIP = "127.0.0.1"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(65536)]
    [InlineData(100000)]
    public void Validate_WithInvalidHostPort_ShouldHaveValidationError(int invalidPort)
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = invalidPort,
            ContainerPort = 80,
            Protocol = "TCP"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.HostPort)
            .WithErrorMessage("Host port must be between 1 and 65535");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(65536)]
    [InlineData(100000)]
    public void Validate_WithInvalidContainerPort_ShouldHaveValidationError(int invalidPort)
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = invalidPort,
            Protocol = "TCP"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ContainerPort)
            .WithErrorMessage("Container port must be between 1 and 65535");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithEmptyProtocol_ShouldHaveValidationError(string protocol)
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = 80,
            Protocol = protocol
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Protocol)
            .WithErrorMessage("Protocol is required");
    }

    [Theory]
    [InlineData("INVALID")]
    [InlineData("HTTP")]
    [InlineData("FTP")]
    public void Validate_WithInvalidProtocol_ShouldHaveValidationError(string invalidProtocol)
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = 80,
            Protocol = invalidProtocol
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Protocol)
            .WithErrorMessage("Protocol must be TCP or UDP");
    }

    [Theory]
    [InlineData("TCP")]
    [InlineData("UDP")]
    [InlineData("tcp")]
    [InlineData("udp")]
    public void Validate_WithValidProtocol_ShouldNotHaveValidationError(string validProtocol)
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = 80,
            Protocol = validProtocol
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Protocol);
    }

    [Theory]
    [InlineData("invalid-ip")]
    [InlineData("256.256.256.256")]
    [InlineData("192.168.1")]
    [InlineData("***********.1")]
    public void Validate_WithInvalidHostIP_ShouldHaveValidationError(string invalidIP)
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = 80,
            Protocol = "TCP",
            HostIP = invalidIP
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.HostIP)
            .WithErrorMessage("Host IP must be a valid IP address");
    }

    [Theory]
    [InlineData("127.0.0.1")]
    [InlineData("***********")]
    [InlineData("0.0.0.0")]
    [InlineData("::1")]
    [InlineData("2001:db8::1")]
    public void Validate_WithValidHostIP_ShouldNotHaveValidationError(string validIP)
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = 80,
            Protocol = "TCP",
            HostIP = validIP
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.HostIP);
    }

    [Fact]
    public void Validate_WithNullHostIP_ShouldNotHaveValidationError()
    {
        // Arrange
        var dto = new CreatePortMappingDto
        {
            HostPort = 8080,
            ContainerPort = 80,
            Protocol = "TCP",
            HostIP = null
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.HostIP);
    }
}

/// <summary>
/// Unit tests for CreateVolumeMountDtoValidator
/// </summary>
public class CreateVolumeMountDtoValidatorTests
{
    private readonly CreateVolumeMountDtoValidator _validator;

    public CreateVolumeMountDtoValidatorTests()
    {
        _validator = new CreateVolumeMountDtoValidator();
    }

    [Fact]
    public void Validate_WithValidVolumeMount_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = "/container/data",
            Type = "bind",
            IsReadOnly = false
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithEmptySource_ShouldHaveValidationError(string source)
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = source,
            ContainerPath = "/container/data",
            Type = "bind"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Source)
            .WithErrorMessage("Volume source is required");
    }

    [Fact]
    public void Validate_WithTooLongSource_ShouldHaveValidationError()
    {
        // Arrange
        var longSource = new string('a', 501); // 501 characters
        var dto = new CreateVolumeMountDto
        {
            Source = longSource,
            ContainerPath = "/container/data",
            Type = "bind"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Source)
            .WithErrorMessage("Volume source cannot exceed 500 characters");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithEmptyContainerPath_ShouldHaveValidationError(string containerPath)
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = containerPath,
            Type = "bind"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ContainerPath)
            .WithErrorMessage("Container path is required");
    }

    [Fact]
    public void Validate_WithTooLongContainerPath_ShouldHaveValidationError()
    {
        // Arrange
        var longPath = "/" + new string('a', 500); // 501 characters total
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = longPath,
            Type = "bind"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ContainerPath)
            .WithErrorMessage("Container path cannot exceed 500 characters");
    }

    [Theory]
    [InlineData("relative/path")]
    [InlineData("not-absolute")]
    [InlineData("./relative")]
    [InlineData("../relative")]
    public void Validate_WithRelativeContainerPath_ShouldHaveValidationError(string relativePath)
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = relativePath,
            Type = "bind"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ContainerPath)
            .WithErrorMessage("Container path must be absolute");
    }

    [Theory]
    [InlineData("/absolute/path")]
    [InlineData("/")]
    [InlineData("/data")]
    [InlineData("C:\\Windows\\Path")] // Windows absolute path
    public void Validate_WithAbsoluteContainerPath_ShouldNotHaveValidationError(string absolutePath)
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = absolutePath,
            Type = "bind"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.ContainerPath);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithEmptyType_ShouldHaveValidationError(string type)
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = "/container/data",
            Type = type
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Type)
            .WithErrorMessage("Mount type is required");
    }

    [Theory]
    [InlineData("invalid")]
    [InlineData("unknown")]
    [InlineData("nfs")]
    public void Validate_WithInvalidType_ShouldHaveValidationError(string invalidType)
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = "/container/data",
            Type = invalidType
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Type)
            .WithErrorMessage("Mount type must be bind, volume, or tmpfs");
    }

    [Theory]
    [InlineData("bind")]
    [InlineData("volume")]
    [InlineData("tmpfs")]
    [InlineData("BIND")]
    [InlineData("Volume")]
    [InlineData("Tmpfs")]
    public void Validate_WithValidType_ShouldNotHaveValidationError(string validType)
    {
        // Arrange
        var dto = new CreateVolumeMountDto
        {
            Source = "/host/data",
            ContainerPath = "/container/data",
            Type = validType
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Type);
    }
}

/// <summary>
/// Unit tests for CreateEnvironmentVariableDtoValidator
/// </summary>
public class CreateEnvironmentVariableDtoValidatorTests
{
    private readonly CreateEnvironmentVariableDtoValidator _validator;

    public CreateEnvironmentVariableDtoValidatorTests()
    {
        _validator = new CreateEnvironmentVariableDtoValidator();
    }

    [Fact]
    public void Validate_WithValidEnvironmentVariable_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = "NODE_ENV",
            Value = "production",
            IsSensitive = false
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithEmptyName_ShouldHaveValidationError(string name)
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = name,
            Value = "test-value"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Environment variable name is required");
    }

    [Fact]
    public void Validate_WithTooLongName_ShouldHaveValidationError()
    {
        // Arrange
        var longName = new string('A', 256); // 256 characters
        var dto = new CreateEnvironmentVariableDto
        {
            Name = longName,
            Value = "test-value"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Environment variable name cannot exceed 255 characters");
    }

    [Theory]
    [InlineData("123INVALID")] // Starts with number
    [InlineData("-INVALID")] // Starts with hyphen
    [InlineData("INVALID-NAME")] // Contains hyphen
    [InlineData("INVALID NAME")] // Contains space
    [InlineData("INVALID@NAME")] // Contains special character
    public void Validate_WithInvalidNameFormat_ShouldHaveValidationError(string invalidName)
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = invalidName,
            Value = "test-value"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Environment variable name must start with letter or underscore and contain only letters, numbers, and underscores");
    }

    [Theory]
    [InlineData("VALID_NAME")]
    [InlineData("_VALID_NAME")]
    [InlineData("ValidName123")]
    [InlineData("NODE_ENV")]
    [InlineData("DATABASE_URL")]
    [InlineData("_")]
    [InlineData("A")]
    public void Validate_WithValidNameFormat_ShouldNotHaveValidationError(string validName)
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = validName,
            Value = "test-value"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_WithNullValue_ShouldHaveValidationError()
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = "TEST_VAR",
            Value = null!
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Value)
            .WithErrorMessage("Environment variable value cannot be null");
    }

    [Fact]
    public void Validate_WithEmptyValue_ShouldNotHaveValidationError()
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = "TEST_VAR",
            Value = ""
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Value);
    }

    [Fact]
    public void Validate_WithTooLongValue_ShouldHaveValidationError()
    {
        // Arrange
        var longValue = new string('a', 32768); // 32768 characters
        var dto = new CreateEnvironmentVariableDto
        {
            Name = "TEST_VAR",
            Value = longValue
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Value)
            .WithErrorMessage("Environment variable value cannot exceed 32767 characters");
    }

    [Fact]
    public void Validate_WithMaxLengthValue_ShouldNotHaveValidationError()
    {
        // Arrange
        var maxValue = new string('a', 32767); // Exactly 32767 characters
        var dto = new CreateEnvironmentVariableDto
        {
            Name = "TEST_VAR",
            Value = maxValue
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Value);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    [InlineData(null)]
    public void Validate_WithAnySensitiveValue_ShouldNotHaveValidationError(bool? isSensitive)
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = "TEST_VAR",
            Value = "test-value",
            IsSensitive = isSensitive
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.IsSensitive);
    }

    [Fact]
    public void Validate_WithComplexValidEnvironmentVariable_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var dto = new CreateEnvironmentVariableDto
        {
            Name = "DATABASE_CONNECTION_STRING",
            Value = "Server=localhost;Database=MyApp;User Id=sa;Password=MyComplexPassword123!;",
            IsSensitive = true
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }
}
