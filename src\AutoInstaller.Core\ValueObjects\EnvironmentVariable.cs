namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker environment variable
/// </summary>
public sealed record EnvironmentVariable
{
    /// <summary>
    /// Environment variable name
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// Environment variable value
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Whether this variable contains sensitive information
    /// </summary>
    public bool IsSensitive { get; }

    /// <summary>
    /// Initialize a new environment variable
    /// </summary>
    /// <param name="name">Variable name</param>
    /// <param name="value">Variable value</param>
    /// <param name="isSensitive">Whether variable is sensitive</param>
    private EnvironmentVariable(string name, string value, bool isSensitive)
    {
        Name = name;
        Value = value;
        IsSensitive = isSensitive;
    }

    /// <summary>
    /// Create a new environment variable
    /// </summary>
    /// <param name="name">Variable name</param>
    /// <param name="value">Variable value</param>
    /// <param name="isSensitive">Whether variable contains sensitive data (default: auto-detect)</param>
    /// <returns>Environment variable instance</returns>
    /// <exception cref="ArgumentException">Thrown when name is invalid</exception>
    public static EnvironmentVariable Create(string name, string value, bool? isSensitive = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Environment variable name cannot be null or empty", nameof(name));

        ValidateName(name);

        // Auto-detect sensitive variables if not specified
        var sensitive = isSensitive ?? IsSensitiveName(name);

        return new EnvironmentVariable(name, value ?? string.Empty, sensitive);
    }

    /// <summary>
    /// Create environment variable from string format (e.g., "NAME=value")
    /// </summary>
    /// <param name="envString">Environment variable string</param>
    /// <param name="isSensitive">Whether variable is sensitive (default: auto-detect)</param>
    /// <returns>Environment variable instance</returns>
    /// <exception cref="ArgumentException">Thrown when format is invalid</exception>
    public static EnvironmentVariable FromString(string envString, bool? isSensitive = null)
    {
        if (string.IsNullOrWhiteSpace(envString))
            throw new ArgumentException("Environment variable string cannot be null or empty", nameof(envString));

        var equalIndex = envString.IndexOf('=');
        if (equalIndex == -1)
        {
            // No value specified, treat as empty value
            return Create(envString, string.Empty, isSensitive);
        }

        var name = envString[..equalIndex];
        var value = envString[(equalIndex + 1)..];

        return Create(name, value, isSensitive);
    }

    /// <summary>
    /// Get string representation in Docker format
    /// </summary>
    /// <returns>Environment variable string</returns>
    public string ToDockerString()
    {
        return $"{Name}={Value}";
    }

    /// <summary>
    /// Get display value (masked if sensitive)
    /// </summary>
    /// <returns>Display-safe value</returns>
    public string GetDisplayValue()
    {
        if (IsSensitive && !string.IsNullOrEmpty(Value))
        {
            return "***";
        }
        return Value;
    }

    /// <summary>
    /// Get display string (masked if sensitive)
    /// </summary>
    /// <returns>Display-safe string</returns>
    public string ToDisplayString()
    {
        return $"{Name}={GetDisplayValue()}";
    }

    /// <summary>
    /// Check if variable name is empty or null
    /// </summary>
    /// <returns>True if value is empty or null</returns>
    public bool IsEmpty()
    {
        return string.IsNullOrEmpty(Value);
    }

    /// <summary>
    /// Create a copy with new value
    /// </summary>
    /// <param name="newValue">New value</param>
    /// <returns>New environment variable with updated value</returns>
    public EnvironmentVariable WithValue(string newValue)
    {
        return Create(Name, newValue, IsSensitive);
    }

    /// <summary>
    /// Create a copy with updated sensitivity
    /// </summary>
    /// <param name="sensitive">Whether variable should be sensitive</param>
    /// <returns>New environment variable with updated sensitivity</returns>
    public EnvironmentVariable WithSensitivity(bool sensitive)
    {
        return Create(Name, Value, sensitive);
    }

    /// <summary>
    /// Validate environment variable name
    /// </summary>
    /// <param name="name">Name to validate</param>
    /// <exception cref="ArgumentException">Thrown when name is invalid</exception>
    private static void ValidateName(string name)
    {
        if (name.Length > 255)
            throw new ArgumentException("Environment variable name cannot be longer than 255 characters", nameof(name));

        if (!char.IsLetter(name[0]) && name[0] != '_')
            throw new ArgumentException("Environment variable name must start with a letter or underscore", nameof(name));

        if (!name.All(c => char.IsLetterOrDigit(c) || c == '_'))
            throw new ArgumentException("Environment variable name can only contain letters, digits, and underscores", nameof(name));
    }

    /// <summary>
    /// Check if variable name suggests sensitive content
    /// </summary>
    /// <param name="name">Variable name to check</param>
    /// <returns>True if name suggests sensitive content</returns>
    private static bool IsSensitiveName(string name)
    {
        var lowerName = name.ToLowerInvariant();
        
        var sensitiveKeywords = new[]
        {
            "password", "passwd", "pwd", "secret", "key", "token", "auth",
            "credential", "cred", "api_key", "apikey", "private", "cert",
            "certificate", "ssl", "tls", "hash", "salt", "signature"
        };

        return sensitiveKeywords.Any(keyword => lowerName.Contains(keyword));
    }

    /// <summary>
    /// String representation of environment variable
    /// </summary>
    /// <returns>Environment variable string</returns>
    public override string ToString() => ToDockerString();
}
