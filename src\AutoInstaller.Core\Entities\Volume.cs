using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Events;

namespace AutoInstaller.Core.Entities;

/// <summary>
/// Represents a Docker volume entity in the domain
/// </summary>
public class Volume
{
    private readonly List<DomainEvent> _domainEvents = new();
    private readonly List<ContainerId> _mountedContainers = new();

    /// <summary>
    /// Initialize a new volume
    /// </summary>
    /// <param name="id">Volume identifier</param>
    /// <param name="name">Volume name</param>
    /// <param name="driver">Volume driver</param>
    /// <param name="mountpoint">Volume mountpoint on host</param>
    private Volume(VolumeId id, string name, string driver, string mountpoint)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Name = !string.IsNullOrWhiteSpace(name) ? name : throw new ArgumentException("Volume name cannot be empty", nameof(name));
        Driver = !string.IsNullOrWhiteSpace(driver) ? driver : throw new ArgumentException("Driver cannot be empty", nameof(driver));
        Mountpoint = !string.IsNullOrWhiteSpace(mountpoint) ? mountpoint : throw new ArgumentException("Mountpoint cannot be empty", nameof(mountpoint));
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Volume unique identifier
    /// </summary>
    public VolumeId Id { get; private set; }

    /// <summary>
    /// Volume name
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// Volume driver (local, nfs, etc.)
    /// </summary>
    public string Driver { get; private set; }

    /// <summary>
    /// Volume mountpoint on the host system
    /// </summary>
    public string Mountpoint { get; private set; }

    /// <summary>
    /// Volume creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; private set; }

    /// <summary>
    /// Volume description
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Volume size in bytes (if available)
    /// </summary>
    public long? Size { get; private set; }

    /// <summary>
    /// Whether the volume is read-only
    /// </summary>
    public bool IsReadOnly { get; private set; }

    /// <summary>
    /// Containers that have this volume mounted
    /// </summary>
    public IReadOnlyList<ContainerId> MountedContainers => _mountedContainers.AsReadOnly();

    /// <summary>
    /// Number of containers using this volume
    /// </summary>
    public int MountCount => _mountedContainers.Count;

    /// <summary>
    /// Whether this volume is currently in use
    /// </summary>
    public bool IsInUse => _mountedContainers.Any();

    /// <summary>
    /// Domain events raised by this entity
    /// </summary>
    public IReadOnlyList<DomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Create a new volume
    /// </summary>
    /// <param name="name">Volume name</param>
    /// <param name="driver">Volume driver</param>
    /// <param name="mountpoint">Volume mountpoint</param>
    /// <param name="description">Optional description</param>
    /// <returns>New volume instance</returns>
    public static Volume Create(string name, string driver, string mountpoint, string? description = null)
    {
        var volume = new Volume(
            VolumeId.New(),
            name,
            driver,
            mountpoint
        );

        if (!string.IsNullOrWhiteSpace(description))
        {
            volume.Description = description;
        }

        volume.AddDomainEvent(new VolumeCreatedEvent(volume.Id, volume.Name, volume.Driver));
        return volume;
    }

    /// <summary>
    /// Mount volume to a container
    /// </summary>
    /// <param name="containerId">Container to mount to</param>
    public void MountToContainer(ContainerId containerId)
    {
        ArgumentNullException.ThrowIfNull(containerId);

        if (_mountedContainers.Contains(containerId))
            return; // Already mounted

        _mountedContainers.Add(containerId);
        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new VolumeMountedEvent(Id, Name, containerId));
    }

    /// <summary>
    /// Unmount volume from a container
    /// </summary>
    /// <param name="containerId">Container to unmount from</param>
    public void UnmountFromContainer(ContainerId containerId)
    {
        ArgumentNullException.ThrowIfNull(containerId);

        if (_mountedContainers.Remove(containerId))
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new VolumeUnmountedEvent(Id, Name, containerId));
        }
    }

    /// <summary>
    /// Check if volume is mounted to a specific container
    /// </summary>
    /// <param name="containerId">Container to check</param>
    /// <returns>True if volume is mounted to the container</returns>
    public bool IsMountedToContainer(ContainerId containerId)
    {
        return containerId != null && _mountedContainers.Contains(containerId);
    }

    /// <summary>
    /// Update volume size
    /// </summary>
    /// <param name="size">New size in bytes</param>
    public void UpdateSize(long? size)
    {
        if (size.HasValue && size < 0)
            throw new ArgumentException("Size cannot be negative", nameof(size));

        if (Size != size)
        {
            var oldSize = Size;
            Size = size;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new VolumeSizeUpdatedEvent(Id, Name, oldSize, size));
        }
    }

    /// <summary>
    /// Set volume as read-only or read-write
    /// </summary>
    /// <param name="isReadOnly">Whether volume should be read-only</param>
    public void SetReadOnly(bool isReadOnly)
    {
        if (IsReadOnly != isReadOnly)
        {
            IsReadOnly = isReadOnly;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new VolumeConfigurationUpdatedEvent(Id, Name));
        }
    }

    /// <summary>
    /// Update volume description
    /// </summary>
    /// <param name="description">New description</param>
    public void UpdateDescription(string? description)
    {
        if (Description != description)
        {
            Description = description;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new VolumeUpdatedEvent(Id, Name));
        }
    }

    /// <summary>
    /// Update volume mountpoint
    /// </summary>
    /// <param name="mountpoint">New mountpoint</param>
    public void UpdateMountpoint(string mountpoint)
    {
        if (string.IsNullOrWhiteSpace(mountpoint))
            throw new ArgumentException("Mountpoint cannot be empty", nameof(mountpoint));

        if (Mountpoint != mountpoint)
        {
            var oldMountpoint = Mountpoint;
            Mountpoint = mountpoint;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new VolumeMountpointUpdatedEvent(Id, Name, oldMountpoint, mountpoint));
        }
    }

    /// <summary>
    /// Mark volume for removal
    /// </summary>
    public void MarkForRemoval()
    {
        if (IsInUse)
            throw new InvalidOperationException("Cannot remove volume that is currently in use");

        AddDomainEvent(new VolumeMarkedForRemovalEvent(Id, Name));
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Get formatted size string
    /// </summary>
    /// <returns>Human-readable size string or "Unknown" if size is not available</returns>
    public string GetFormattedSize()
    {
        if (!Size.HasValue)
            return "Unknown";

        const long KB = 1024;
        const long MB = KB * 1024;
        const long GB = MB * 1024;

        return Size.Value switch
        {
            < KB => $"{Size.Value} B",
            < MB => $"{Size.Value / (double)KB:F1} KB",
            < GB => $"{Size.Value / (double)MB:F1} MB",
            _ => $"{Size.Value / (double)GB:F1} GB"
        };
    }

    /// <summary>
    /// Clear all domain events
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Add domain event
    /// </summary>
    /// <param name="domainEvent">Domain event to add</param>
    private void AddDomainEvent(DomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
}
