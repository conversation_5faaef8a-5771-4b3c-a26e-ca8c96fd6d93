using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using Docker.DotNet;
using Docker.DotNet.Models;
using AutoInstaller.Infrastructure.Repositories;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Interfaces.Repositories;

namespace AutoInstaller.Tests.Unit.Infrastructure.Repositories;

/// <summary>
/// Unit tests for ContainerRepository
/// </summary>
public class ContainerRepositoryTests : IDisposable
{
    private readonly Mock<IDockerClient> _mockDockerClient;
    private readonly Mock<IContainerOperations> _mockContainerOperations;
    private readonly Mock<ILogger<ContainerRepository>> _mockLogger;
    private readonly ContainerRepository _repository;

    public ContainerRepositoryTests()
    {
        _mockDockerClient = new Mock<IDockerClient>();
        _mockContainerOperations = new Mock<IContainerOperations>();
        _mockLogger = new Mock<ILogger<ContainerRepository>>();

        _mockDockerClient.Setup(x => x.Containers).Returns(_mockContainerOperations.Object);

        _repository = new ContainerRepository(
            _mockDockerClient.Object as DockerClient ?? throw new InvalidOperationException("Failed to cast to DockerClient"),
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetByIdAsync_WithValidId_ShouldReturnContainer()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");
        var containerResponse = CreateMockContainerResponse(containerId.Value, "test-container", "nginx:latest");

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse);

        // Act
        var result = await _repository.GetByIdAsync(containerId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Value.Should().Be(containerId.Value);
        result.Name.Should().Be("test-container");
        result.ImageTag.Value.Should().Be("nginx:latest");

        _mockContainerOperations.Verify(
            x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetByIdAsync_WithNonExistentId_ShouldReturnNull()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DockerContainerNotFoundException(System.Net.HttpStatusCode.NotFound, "Container not found"));

        // Act
        var result = await _repository.GetByIdAsync(containerId);

        // Assert
        result.Should().BeNull();

        _mockContainerOperations.Verify(
            x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetByIdAsync_WithDockerException_ShouldThrowException()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DockerApiException(System.Net.HttpStatusCode.InternalServerError, "Docker error"));

        // Act & Assert
        await _repository.Invoking(r => r.GetByIdAsync(containerId))
            .Should().ThrowAsync<DockerApiException>();

        _mockContainerOperations.Verify(
            x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetByNameAsync_WithValidName_ShouldReturnContainer()
    {
        // Arrange
        var containerName = "test-container";
        var containerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        
        var containerSummary = new ContainerListResponse
        {
            ID = containerId,
            Names = new List<string> { "/test-container" }
        };

        var containerResponse = CreateMockContainerResponse(containerId, containerName, "nginx:latest");

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ContainerListResponse> { containerSummary });

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse);

        // Act
        var result = await _repository.GetByNameAsync(containerName);

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be(containerName);
        result.Id.Value.Should().Be(containerId);

        _mockContainerOperations.Verify(
            x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _mockContainerOperations.Verify(
            x => x.InspectContainerAsync(containerId, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetByNameAsync_WithNonExistentName_ShouldReturnNull()
    {
        // Arrange
        var containerName = "non-existent-container";

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ContainerListResponse>());

        // Act
        var result = await _repository.GetByNameAsync(containerName);

        // Assert
        result.Should().BeNull();

        _mockContainerOperations.Verify(
            x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetAllAsync_WithMultipleContainers_ShouldReturnAllContainers()
    {
        // Arrange
        var container1Id = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var container2Id = "b1c2d3e4f5a6789012345678901234567890123456789012345678901234";

        var containerSummaries = new List<ContainerListResponse>
        {
            new() { ID = container1Id, Names = new List<string> { "/container1" } },
            new() { ID = container2Id, Names = new List<string> { "/container2" } }
        };

        var containerResponse1 = CreateMockContainerResponse(container1Id, "container1", "nginx:latest");
        var containerResponse2 = CreateMockContainerResponse(container2Id, "container2", "redis:alpine");

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerSummaries);

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(container1Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse1);

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(container2Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse2);

        // Act
        var result = await _repository.GetAllAsync();

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(c => c.Name == "container1");
        result.Should().Contain(c => c.Name == "container2");

        _mockContainerOperations.Verify(
            x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _mockContainerOperations.Verify(
            x => x.InspectContainerAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Exactly(2));
    }

    [Fact]
    public async Task GetAllAsync_WithInspectionFailure_ShouldSkipFailedContainer()
    {
        // Arrange
        var container1Id = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var container2Id = "b1c2d3e4f5a6789012345678901234567890123456789012345678901234";

        var containerSummaries = new List<ContainerListResponse>
        {
            new() { ID = container1Id, Names = new List<string> { "/container1" } },
            new() { ID = container2Id, Names = new List<string> { "/container2" } }
        };

        var containerResponse1 = CreateMockContainerResponse(container1Id, "container1", "nginx:latest");

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerSummaries);

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(container1Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse1);

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(container2Id, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DockerApiException(System.Net.HttpStatusCode.NotFound, "Container not found"));

        // Act
        var result = await _repository.GetAllAsync();

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(c => c.Name == "container1");

        _mockContainerOperations.Verify(
            x => x.InspectContainerAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Exactly(2));
    }

    [Fact]
    public async Task ExistsAsync_WithExistingContainer_ShouldReturnTrue()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");
        var containerResponse = CreateMockContainerResponse(containerId.Value, "test-container", "nginx:latest");

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse);

        // Act
        var result = await _repository.ExistsAsync(containerId);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ExistsAsync_WithNonExistentContainer_ShouldReturnFalse()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DockerContainerNotFoundException(System.Net.HttpStatusCode.NotFound, "Container not found"));

        // Act
        var result = await _repository.ExistsAsync(containerId);

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// Create mock container response for testing
    /// </summary>
    private static ContainerInspectResponse CreateMockContainerResponse(string id, string name, string image)
    {
        return new ContainerInspectResponse
        {
            ID = id,
            Name = $"/{name}",
            Config = new Config { Image = image },
            State = new ContainerState { Status = "running" }
        };
    }

    [Fact]
    public async Task IsNameAvailableAsync_WithAvailableName_ShouldReturnTrue()
    {
        // Arrange
        var containerName = "available-name";

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ContainerListResponse>());

        // Act
        var result = await _repository.IsNameAvailableAsync(containerName);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsNameAvailableAsync_WithUnavailableName_ShouldReturnFalse()
    {
        // Arrange
        var containerName = "existing-container";
        var containerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";

        var containerSummary = new ContainerListResponse
        {
            ID = containerId,
            Names = new List<string> { "/existing-container" }
        };

        var containerResponse = CreateMockContainerResponse(containerId, containerName, "nginx:latest");

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ContainerListResponse> { containerSummary });

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse);

        // Act
        var result = await _repository.IsNameAvailableAsync(containerName);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsNameAvailableAsync_WithExcludeId_ShouldReturnTrue()
    {
        // Arrange
        var containerName = "existing-container";
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        var containerSummary = new ContainerListResponse
        {
            ID = containerId.Value,
            Names = new List<string> { "/existing-container" }
        };

        var containerResponse = CreateMockContainerResponse(containerId.Value, containerName, "nginx:latest");

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ContainerListResponse> { containerSummary });

        _mockContainerOperations
            .Setup(x => x.InspectContainerAsync(containerId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerResponse);

        // Act
        var result = await _repository.IsNameAvailableAsync(containerName, containerId);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task AddAsync_WithValidContainer_ShouldCreateContainer()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");
        var imageTag = ImageTag.From("nginx:latest");
        var container = Container.Create(containerId, "test-container", imageTag, "Test container");

        var createResponse = new CreateContainerResponse
        {
            ID = containerId.Value,
            Warnings = new List<string>()
        };

        _mockContainerOperations
            .Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createResponse);

        // Act
        await _repository.AddAsync(container);

        // Assert
        container.Id.Value.Should().Be(containerId.Value);

        _mockContainerOperations.Verify(
            x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task AddAsync_WithDockerException_ShouldThrowException()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");
        var imageTag = ImageTag.From("nginx:latest");
        var container = Container.Create(containerId, "test-container", imageTag, "Test container");

        _mockContainerOperations
            .Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DockerApiException(System.Net.HttpStatusCode.BadRequest, "Invalid parameters"));

        // Act & Assert
        await _repository.Invoking(r => r.AddAsync(container))
            .Should().ThrowAsync<DockerApiException>();

        _mockContainerOperations.Verify(
            x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task RemoveAsync_WithValidContainer_ShouldRemoveContainer()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");
        var imageTag = ImageTag.From("nginx:latest");
        var container = Container.Create(containerId, "test-container", imageTag, "Test container");

        _mockContainerOperations
            .Setup(x => x.RemoveContainerAsync(
                containerId.Value,
                It.IsAny<ContainerRemoveParameters>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _repository.RemoveAsync(container);

        // Assert
        _mockContainerOperations.Verify(
            x => x.RemoveContainerAsync(
                containerId.Value,
                It.Is<ContainerRemoveParameters>(p => p.Force == true),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task RemoveByIdAsync_WithValidId_ShouldRemoveContainer()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        _mockContainerOperations
            .Setup(x => x.RemoveContainerAsync(
                containerId.Value,
                It.IsAny<ContainerRemoveParameters>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _repository.RemoveByIdAsync(containerId);

        // Assert
        _mockContainerOperations.Verify(
            x => x.RemoveContainerAsync(
                containerId.Value,
                It.Is<ContainerRemoveParameters>(p => p.Force == true),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetCountAsync_WithMultipleContainers_ShouldReturnCorrectCount()
    {
        // Arrange
        var containerSummaries = new List<ContainerListResponse>
        {
            new() { ID = "container1", Names = new List<string> { "/container1" } },
            new() { ID = "container2", Names = new List<string> { "/container2" } },
            new() { ID = "container3", Names = new List<string> { "/container3" } }
        };

        _mockContainerOperations
            .Setup(x => x.ListContainersAsync(It.IsAny<ContainersListParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(containerSummaries);

        // Act
        var result = await _repository.GetCountAsync();

        // Assert
        result.Should().Be(3);

        _mockContainerOperations.Verify(
            x => x.ListContainersAsync(
                It.Is<ContainersListParameters>(p => p.All == true),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_WithValidContainer_ShouldCompleteSuccessfully()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");
        var imageTag = ImageTag.From("nginx:latest");
        var container = Container.Create(containerId, "test-container", imageTag, "Test container");

        // Act
        await _repository.UpdateAsync(container);

        // Assert - UpdateAsync is mainly for state tracking, so we just verify it completes
        // No Docker API calls are expected for update operations
        _mockContainerOperations.Verify(
            x => x.InspectContainerAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    public void Dispose()
    {
        // Clean up if needed
    }
}
