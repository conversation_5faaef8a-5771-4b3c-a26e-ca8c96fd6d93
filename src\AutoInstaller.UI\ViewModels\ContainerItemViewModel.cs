using ReactiveUI;
using AutoInstaller.Application.Queries;

namespace AutoInstaller.UI.ViewModels;

/// <summary>
/// View model for a container item in the list
/// </summary>
public sealed class ContainerItemViewModel : ReactiveObject
{
    /// <summary>
    /// Container ID
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// Container name
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// Image tag
    /// </summary>
    public string ImageTag { get; }

    /// <summary>
    /// Container status
    /// </summary>
    private string _status;
    public string Status
    {
        get => _status;
        set => this.RaiseAndSetIfChanged(ref _status, value);
    }

    /// <summary>
    /// Status display name in Portuguese
    /// </summary>
    private string _statusDisplayName;
    public string StatusDisplayName
    {
        get => _statusDisplayName;
        set => this.RaiseAndSetIfChanged(ref _statusDisplayName, value);
    }

    /// <summary>
    /// Status color for UI
    /// </summary>
    private string _statusColor;
    public string StatusColor
    {
        get => _statusColor;
        set => this.RaiseAndSetIfChanged(ref _statusColor, value);
    }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; }

    /// <summary>
    /// Container description
    /// </summary>
    public string? Description { get; }

    /// <summary>
    /// Port mappings display text
    /// </summary>
    public string PortMappingsText { get; }

    /// <summary>
    /// Volume mounts display text
    /// </summary>
    public string VolumeMountsText { get; }

    /// <summary>
    /// Environment variables count
    /// </summary>
    public int EnvironmentVariablesCount { get; }

    /// <summary>
    /// Networks display text
    /// </summary>
    public string NetworksText { get; }

    /// <summary>
    /// Whether the container is running
    /// </summary>
    public bool IsRunning => Status.Equals("Running", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Whether the container is stopped
    /// </summary>
    public bool IsStopped => Status.Equals("Stopped", StringComparison.OrdinalIgnoreCase) || 
                            Status.Equals("Exited", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Whether the container can be started
    /// </summary>
    public bool CanStart => IsStopped || Status.Equals("Created", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Whether the container can be stopped
    /// </summary>
    public bool CanStop => IsRunning || Status.Equals("Paused", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Whether the container can be removed
    /// </summary>
    public bool CanRemove => !IsRunning;

    /// <summary>
    /// Initialize container item view model
    /// </summary>
    /// <param name="container">Container DTO</param>
    public ContainerItemViewModel(ContainerDto container)
    {
        Id = container.Id;
        Name = container.Name;
        ImageTag = container.ImageTag;
        _status = container.Status;
        _statusDisplayName = container.StatusDisplayName;
        _statusColor = container.StatusColor;
        CreatedAt = container.CreatedAt;
        UpdatedAt = container.UpdatedAt;
        Description = container.Description;

        // Format port mappings
        if (container.PortMappings.Any())
        {
            PortMappingsText = string.Join(", ", container.PortMappings.Select(p => p.DisplayString));
        }
        else
        {
            PortMappingsText = "Nenhuma";
        }

        // Format volume mounts
        if (container.VolumeMounts.Any())
        {
            VolumeMountsText = string.Join(", ", container.VolumeMounts.Select(v => v.DisplayString));
        }
        else
        {
            VolumeMountsText = "Nenhum";
        }

        // Environment variables count
        EnvironmentVariablesCount = container.EnvironmentVariables.Count;

        // Format networks
        if (container.Networks.Any())
        {
            NetworksText = string.Join(", ", container.Networks);
        }
        else
        {
            NetworksText = "Nenhuma";
        }
    }

    /// <summary>
    /// Get formatted creation time
    /// </summary>
    /// <returns>Formatted creation time</returns>
    public string GetFormattedCreatedAt()
    {
        var timeSpan = DateTime.UtcNow - CreatedAt;
        
        return timeSpan.TotalDays switch
        {
            >= 1 => $"{(int)timeSpan.TotalDays} dia(s) atrás",
            _ when timeSpan.TotalHours >= 1 => $"{(int)timeSpan.TotalHours} hora(s) atrás",
            _ when timeSpan.TotalMinutes >= 1 => $"{(int)timeSpan.TotalMinutes} minuto(s) atrás",
            _ => "Agora mesmo"
        };
    }

    /// <summary>
    /// Get formatted update time
    /// </summary>
    /// <returns>Formatted update time</returns>
    public string GetFormattedUpdatedAt()
    {
        var timeSpan = DateTime.UtcNow - UpdatedAt;
        
        return timeSpan.TotalDays switch
        {
            >= 1 => $"{(int)timeSpan.TotalDays} dia(s) atrás",
            _ when timeSpan.TotalHours >= 1 => $"{(int)timeSpan.TotalHours} hora(s) atrás",
            _ when timeSpan.TotalMinutes >= 1 => $"{(int)timeSpan.TotalMinutes} minuto(s) atrás",
            _ => "Agora mesmo"
        };
    }

    /// <summary>
    /// Get status icon
    /// </summary>
    /// <returns>Status icon character</returns>
    public string GetStatusIcon()
    {
        return Status.ToLowerInvariant() switch
        {
            "running" => "▶️",
            "stopped" => "⏹️",
            "exited" => "⏹️",
            "created" => "⚪",
            "paused" => "⏸️",
            "restarting" => "🔄",
            "removed" => "❌",
            _ => "❓"
        };
    }
}
