using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Interfaces.Repositories;

/// <summary>
/// Repository interface for Image entity
/// </summary>
public interface IImageRepository
{
    /// <summary>
    /// Get image by ID
    /// </summary>
    /// <param name="id">Image ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Image if found, null otherwise</returns>
    Task<Image?> GetByIdAsync(ImageId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get image by repository and tag
    /// </summary>
    /// <param name="repository">Image repository</param>
    /// <param name="tag">Image tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Image if found, null otherwise</returns>
    Task<Image?> GetByRepositoryAndTagAsync(string repository, string tag, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get image by full name (repository:tag)
    /// </summary>
    /// <param name="fullName">Full image name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Image if found, null otherwise</returns>
    Task<Image?> GetByFullNameAsync(string fullName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all images
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all images</returns>
    Task<IReadOnlyList<Image>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get images by repository
    /// </summary>
    /// <param name="repository">Repository name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of images in repository</returns>
    Task<IReadOnlyList<Image>> GetByRepositoryAsync(string repository, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dangling images (no tags)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of dangling images</returns>
    Task<IReadOnlyList<Image>> GetDanglingAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get images larger than specified size
    /// </summary>
    /// <param name="sizeInBytes">Minimum size in bytes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of large images</returns>
    Task<IReadOnlyList<Image>> GetLargerThanAsync(long sizeInBytes, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if image exists by ID
    /// </summary>
    /// <param name="id">Image ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if image exists</returns>
    Task<bool> ExistsAsync(ImageId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if image exists by repository and tag
    /// </summary>
    /// <param name="repository">Repository name</param>
    /// <param name="tag">Tag name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if image exists</returns>
    Task<bool> ExistsAsync(string repository, string tag, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add new image
    /// </summary>
    /// <param name="image">Image to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task AddAsync(Image image, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing image
    /// </summary>
    /// <param name="image">Image to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateAsync(Image image, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove image
    /// </summary>
    /// <param name="image">Image to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveAsync(Image image, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove image by ID
    /// </summary>
    /// <param name="id">Image ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveByIdAsync(ImageId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get images with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of images</returns>
    Task<(IReadOnlyList<Image> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search images by repository pattern
    /// </summary>
    /// <param name="repositoryPattern">Repository pattern to search</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching images</returns>
    Task<IReadOnlyList<Image>> SearchByRepositoryAsync(string repositoryPattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get total size of all images
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total size in bytes</returns>
    Task<long> GetTotalSizeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get image count
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total number of images</returns>
    Task<int> GetCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dangling image count
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of dangling images</returns>
    Task<int> GetDanglingCountAsync(CancellationToken cancellationToken = default);
}
