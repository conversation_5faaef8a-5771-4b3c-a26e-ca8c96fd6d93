using System.Text.RegularExpressions;

namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker image tag (repository:tag format)
/// </summary>
public sealed record ImageTag
{
    private static readonly Regex TagValidationRegex = new(
        @"^[a-z0-9]+(?:[._-][a-z0-9]+)*(?:/[a-z0-9]+(?:[._-][a-z0-9]+)*)*:[a-zA-Z0-9][a-zA-Z0-9._-]*$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase
    );

    /// <summary>
    /// Full image tag (repository:tag)
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Repository part of the image tag
    /// </summary>
    public string Repository { get; }

    /// <summary>
    /// Tag part of the image tag
    /// </summary>
    public string Tag { get; }

    /// <summary>
    /// Initialize a new image tag
    /// </summary>
    /// <param name="value">Full image tag value</param>
    /// <param name="repository">Repository name</param>
    /// <param name="tag">Tag name</param>
    private ImageTag(string value, string repository, string tag)
    {
        Value = value;
        Repository = repository;
        Tag = tag;
    }

    /// <summary>
    /// Create a new image tag from full string value
    /// </summary>
    /// <param name="value">Full image tag (repository:tag)</param>
    /// <returns>Image tag instance</returns>
    /// <exception cref="ArgumentException">Thrown when value is invalid</exception>
    public static ImageTag From(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Image tag cannot be null or empty", nameof(value));

        // Handle case where no tag is specified (default to 'latest')
        if (!value.Contains(':'))
        {
            value = $"{value}:latest";
        }

        var parts = value.Split(':', 2);
        if (parts.Length != 2)
            throw new ArgumentException("Image tag must be in format 'repository:tag'", nameof(value));

        var repository = parts[0].Trim();
        var tag = parts[1].Trim();

        ValidateRepository(repository);
        ValidateTag(tag);

        var normalizedValue = $"{repository}:{tag}";
        return new ImageTag(normalizedValue, repository, tag);
    }

    /// <summary>
    /// Create a new image tag from repository and tag parts
    /// </summary>
    /// <param name="repository">Repository name</param>
    /// <param name="tag">Tag name</param>
    /// <returns>Image tag instance</returns>
    public static ImageTag From(string repository, string tag)
    {
        if (string.IsNullOrWhiteSpace(repository))
            throw new ArgumentException("Repository cannot be null or empty", nameof(repository));

        if (string.IsNullOrWhiteSpace(tag))
            tag = "latest";

        ValidateRepository(repository);
        ValidateTag(tag);

        var value = $"{repository}:{tag}";
        return new ImageTag(value, repository, tag);
    }

    /// <summary>
    /// Check if this is a latest tag
    /// </summary>
    /// <returns>True if tag is 'latest'</returns>
    public bool IsLatest()
    {
        return string.Equals(Tag, "latest", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Check if this is an official Docker Hub image
    /// </summary>
    /// <returns>True if repository doesn't contain '/' (official image)</returns>
    public bool IsOfficialImage()
    {
        return !Repository.Contains('/');
    }

    /// <summary>
    /// Get registry hostname if present
    /// </summary>
    /// <returns>Registry hostname or null if using Docker Hub</returns>
    public string? GetRegistry()
    {
        var parts = Repository.Split('/');
        if (parts.Length > 2 || (parts.Length == 2 && parts[0].Contains('.')))
        {
            return parts[0];
        }
        return null; // Docker Hub
    }

    /// <summary>
    /// Get namespace/organization if present
    /// </summary>
    /// <returns>Namespace or null if official image</returns>
    public string? GetNamespace()
    {
        var registry = GetRegistry();
        var parts = Repository.Split('/');
        
        if (registry != null)
        {
            // Has registry, namespace is second part if exists
            return parts.Length > 2 ? parts[1] : null;
        }
        else
        {
            // No registry, namespace is first part if exists
            return parts.Length > 1 ? parts[0] : null;
        }
    }

    /// <summary>
    /// Get image name (without registry and namespace)
    /// </summary>
    /// <returns>Image name</returns>
    public string GetImageName()
    {
        var parts = Repository.Split('/');
        return parts[^1]; // Last part is always the image name
    }

    /// <summary>
    /// Create a new tag with different tag version
    /// </summary>
    /// <param name="newTag">New tag version</param>
    /// <returns>New image tag with updated tag</returns>
    public ImageTag WithTag(string newTag)
    {
        return From(Repository, newTag);
    }

    /// <summary>
    /// Validate repository name
    /// </summary>
    /// <param name="repository">Repository to validate</param>
    /// <exception cref="ArgumentException">Thrown when repository is invalid</exception>
    private static void ValidateRepository(string repository)
    {
        if (string.IsNullOrWhiteSpace(repository))
            throw new ArgumentException("Repository cannot be empty", nameof(repository));

        if (repository.Length > 255)
            throw new ArgumentException("Repository name cannot be longer than 255 characters", nameof(repository));

        if (repository.StartsWith('/') || repository.EndsWith('/'))
            throw new ArgumentException("Repository cannot start or end with '/'", nameof(repository));

        if (repository.Contains("//"))
            throw new ArgumentException("Repository cannot contain consecutive '/' characters", nameof(repository));
    }

    /// <summary>
    /// Validate tag name
    /// </summary>
    /// <param name="tag">Tag to validate</param>
    /// <exception cref="ArgumentException">Thrown when tag is invalid</exception>
    private static void ValidateTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag))
            throw new ArgumentException("Tag cannot be empty", nameof(tag));

        if (tag.Length > 128)
            throw new ArgumentException("Tag cannot be longer than 128 characters", nameof(tag));

        if (tag.StartsWith('.') || tag.StartsWith('-'))
            throw new ArgumentException("Tag cannot start with '.' or '-'", nameof(tag));

        if (!tag.All(c => char.IsLetterOrDigit(c) || c == '.' || c == '_' || c == '-'))
            throw new ArgumentException("Tag can only contain letters, digits, '.', '_', and '-'", nameof(tag));
    }

    /// <summary>
    /// Implicit conversion from string to ImageTag
    /// </summary>
    /// <param name="value">String value</param>
    public static implicit operator ImageTag(string value) => From(value);

    /// <summary>
    /// Implicit conversion from ImageTag to string
    /// </summary>
    /// <param name="imageTag">Image tag</param>
    public static implicit operator string(ImageTag imageTag) => imageTag.Value;

    /// <summary>
    /// String representation of image tag
    /// </summary>
    /// <returns>Full image tag value</returns>
    public override string ToString() => Value;
}
