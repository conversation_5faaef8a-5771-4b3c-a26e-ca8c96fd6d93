using Docker.DotNet;
using Docker.DotNet.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AutoInstaller.Core.Interfaces.Services;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Infrastructure.Configuration;
using DomainNetwork = AutoInstaller.Core.Entities.Network;
using DockerNetwork = Docker.DotNet.Models.Network;

namespace AutoInstaller.Infrastructure.Services;

/// <summary>
/// Docker client service implementation using Docker.DotNet
/// </summary>
public sealed class DockerClientService : IDockerService, IDisposable
{
    private readonly DockerClient _dockerClient;
    private readonly ILogger<DockerClientService> _logger;
    private readonly DockerConfiguration _configuration;
    private bool _disposed;

    /// <summary>
    /// Initialize the Docker client service
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="configuration">Docker configuration</param>
    public DockerClientService(
        ILogger<DockerClientService> logger,
        IOptions<DockerConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;

        // Create Docker client based on configuration
        var dockerClientConfiguration = new DockerClientConfiguration(
            new Uri(_configuration.DockerApiUri));

        _dockerClient = dockerClientConfiguration.CreateClient();

        _logger.LogInformation("Docker client initialized with URI: {DockerUri}", _configuration.DockerApiUri);
    }

    /// <summary>
    /// Check if Docker is available and running
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if Docker is available</returns>
    public async Task<bool> IsDockerAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking Docker availability");
            
            var version = await _dockerClient.System.GetVersionAsync(cancellationToken);
            
            _logger.LogDebug("Docker is available, version: {Version}", version.Version);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Docker is not available: {Error}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Get Docker version information
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Docker version info</returns>
    public async Task<DockerVersionInfo> GetVersionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting Docker version information");
            
            var version = await _dockerClient.System.GetVersionAsync(cancellationToken);
            
            return new DockerVersionInfo(
                Version: version.Version ?? "Unknown",
                ApiVersion: version.APIVersion ?? "Unknown",
                GitCommit: version.GitCommit ?? "Unknown",
                GoVersion: version.GoVersion ?? "Unknown",
                Os: version.Os ?? "Unknown",
                Arch: version.Arch ?? "Unknown"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Docker version: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get Docker system information
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Docker system info</returns>
    public async Task<DockerSystemInfo> GetSystemInfoAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting Docker system information");
            
            var systemInfo = await _dockerClient.System.GetSystemInfoAsync(cancellationToken);
            
            return new DockerSystemInfo(
                ContainersRunning: (int)systemInfo.ContainersRunning,
                ContainersPaused: (int)systemInfo.ContainersPaused,
                ContainersStopped: (int)systemInfo.ContainersStopped,
                Images: (int)systemInfo.Images,
                ServerVersion: systemInfo.ServerVersion ?? "Unknown",
                StorageDriver: systemInfo.Driver ?? "Unknown",
                TotalMemory: systemInfo.MemTotal,
                CpuCount: (int)systemInfo.NCPU
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Docker system info: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Validate container configuration before creation
    /// </summary>
    /// <param name="name">Container name</param>
    /// <param name="imageTag">Image tag</param>
    /// <param name="portMappings">Port mappings</param>
    /// <param name="volumeMounts">Volume mounts</param>
    /// <param name="environmentVariables">Environment variables</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    public async Task<ValidationResult> ValidateContainerConfigurationAsync(
        string name,
        ImageTag imageTag,
        IEnumerable<PortMapping> portMappings,
        IEnumerable<VolumeMount> volumeMounts,
        IEnumerable<EnvironmentVariable> environmentVariables,
        CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        try
        {
            _logger.LogDebug("Validating container configuration for {ContainerName}", name);

            // Validate container name
            if (string.IsNullOrWhiteSpace(name))
            {
                errors.Add("Container name cannot be empty");
            }
            else if (name.Length > 255)
            {
                errors.Add("Container name cannot exceed 255 characters");
            }

            // Check if container name already exists
            try
            {
                var existingContainers = await _dockerClient.Containers.ListContainersAsync(
                    new ContainersListParameters { All = true }, cancellationToken);

                if (existingContainers.Any(c => c.Names.Any(n => n.TrimStart('/') == name)))
                {
                    errors.Add($"Container with name '{name}' already exists");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to check existing containers: {Error}", ex.Message);
                errors.Add("Failed to validate container name uniqueness");
            }

            // Validate image exists
            if (!await ImageExistsLocallyAsync(imageTag, cancellationToken))
            {
                _logger.LogDebug("Image {ImageTag} not found locally, checking registry", imageTag.Value);
                
                if (!await ImageExistsInRegistryAsync(imageTag, cancellationToken))
                {
                    errors.Add($"Image '{imageTag.Value}' not found locally or in registry");
                }
            }

            // Validate port mappings
            var portList = portMappings.ToList();
            var hostPorts = portList.Select(p => p.HostPort).ToList();
            var duplicatePorts = hostPorts.GroupBy(p => p).Where(g => g.Count() > 1).Select(g => g.Key);
            
            if (duplicatePorts.Any())
            {
                errors.Add($"Duplicate host ports found: {string.Join(", ", duplicatePorts)}");
            }

            // Check if ports are available
            foreach (var portMapping in portList)
            {
                if (!await IsPortAvailableAsync(portMapping.HostPort, portMapping.Protocol, cancellationToken))
                {
                    errors.Add($"Port {portMapping.HostPort} is already in use");
                }
            }

            // Validate volume mounts
            foreach (var volumeMount in volumeMounts)
            {
                if (volumeMount.IsBind())
                {
                    if (!await PathExistsAsync(volumeMount.Source, cancellationToken))
                    {
                        errors.Add($"Bind mount source path '{volumeMount.Source}' does not exist");
                    }
                }
            }

            return errors.Any() ? ValidationResult.Failure(errors) : ValidationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate container configuration: {Error}", ex.Message);
            return ValidationResult.Failure($"Validation failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Check if port is available on host
    /// </summary>
    /// <param name="port">Port number</param>
    /// <param name="protocol">Port protocol</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if port is available</returns>
    public async Task<bool> IsPortAvailableAsync(int port, PortProtocol protocol = PortProtocol.TCP, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking if port {Port}/{Protocol} is available", port, protocol);

            // Check if any container is using this port
            var containers = await _dockerClient.Containers.ListContainersAsync(
                new ContainersListParameters { All = false }, cancellationToken);

            foreach (var container in containers)
            {
                if (container.Ports?.Any(p => p.PublicPort == port) == true)
                {
                    _logger.LogDebug("Port {Port} is in use by container {ContainerId}", port, container.ID);
                    return false;
                }
            }

            // TODO: Check if port is available on host system
            // For now, assume available if not used by containers
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check port availability for {Port}: {Error}", port, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Check if image exists locally
    /// </summary>
    /// <param name="imageTag">Image tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if image exists locally</returns>
    public async Task<bool> ImageExistsLocallyAsync(ImageTag imageTag, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking if image {ImageTag} exists locally", imageTag.Value);

            var images = await _dockerClient.Images.ListImagesAsync(
                new ImagesListParameters { All = true }, cancellationToken);

            return images.Any(img => img.RepoTags?.Contains(imageTag.Value) == true);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check if image exists locally: {Error}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Check if image exists in registry
    /// </summary>
    /// <param name="imageTag">Image tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if image exists in registry</returns>
    public async Task<bool> ImageExistsInRegistryAsync(ImageTag imageTag, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking if image {ImageTag} exists in registry", imageTag.Value);

            // Try to inspect the image in registry
            // This is a simplified check - in production, you might want to use registry API
            await _dockerClient.Images.InspectImageAsync(imageTag.Value, cancellationToken);
            return true;
        }
        catch (DockerImageNotFoundException)
        {
            _logger.LogDebug("Image {ImageTag} not found in registry", imageTag.Value);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check if image exists in registry: {Error}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Get available networks for container connection
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available networks</returns>
    public async Task<IReadOnlyList<DomainNetwork>> GetAvailableNetworksAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting available Docker networks");

            var networks = await _dockerClient.Networks.ListNetworksAsync(new NetworksListParameters(), cancellationToken);
            
            var result = new List<DomainNetwork>();

            foreach (var network in networks)
            {
                var networkId = NetworkId.From(network.ID);
                var driver = NetworkDriverExtensions.FromDockerString(network.Driver);
                var scope = NetworkScopeExtensions.FromDockerString(network.Scope);

                var domainNetwork = DomainNetwork.Create(networkId, network.Name, driver, scope);
                result.Add(domainNetwork);
            }

            return result.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available networks: {Error}", ex.Message);
            return Array.Empty<DomainNetwork>();
        }
    }

    /// <summary>
    /// Validate network configuration
    /// </summary>
    public async Task<ValidationResult> ValidateNetworkConfigurationAsync(
        string name,
        NetworkDriver driver,
        string? subnet = null,
        string? gateway = null,
        CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        try
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(name))
            {
                errors.Add("Network name cannot be empty");
            }

            // Check if network name already exists
            var networks = await _dockerClient.Networks.ListNetworksAsync(new NetworksListParameters(), cancellationToken);
            if (networks.Any(n => n.Name == name))
            {
                errors.Add($"Network with name '{name}' already exists");
            }

            return errors.Any() ? ValidationResult.Failure(errors) : ValidationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate network configuration: {Error}", ex.Message);
            return ValidationResult.Failure($"Network validation failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Validate volume configuration
    /// </summary>
    public async Task<ValidationResult> ValidateVolumeConfigurationAsync(
        string name,
        string driver,
        string mountpoint,
        CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        try
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(name))
            {
                errors.Add("Volume name cannot be empty");
            }

            // Check if volume name already exists
            var volumes = await _dockerClient.Volumes.ListAsync(cancellationToken);
            if (volumes.Volumes?.Any(v => v.Name == name) == true)
            {
                errors.Add($"Volume with name '{name}' already exists");
            }

            return errors.Any() ? ValidationResult.Failure(errors) : ValidationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate volume configuration: {Error}", ex.Message);
            return ValidationResult.Failure($"Volume validation failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Check if path exists on host
    /// </summary>
    public async Task<bool> PathExistsAsync(string path, CancellationToken cancellationToken = default)
    {
        try
        {
            return await Task.Run(() => 
            {
                return Directory.Exists(path) || File.Exists(path);
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check if path exists: {Path}", path);
            return false;
        }
    }

    /// <summary>
    /// Get disk usage information
    /// </summary>
    public async Task<DockerDiskUsage> GetDiskUsageAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting Docker disk usage information");

            // Get images size
            var images = await _dockerClient.Images.ListImagesAsync(new ImagesListParameters { All = true }, cancellationToken);
            var imagesSize = images.Sum(i => i.Size);

            // Get containers size
            var containers = await _dockerClient.Containers.ListContainersAsync(new ContainersListParameters { All = true }, cancellationToken);
            var containersSize = containers.Sum(c => c.SizeRw);

            // Get volumes size (simplified - Docker.DotNet may not have detailed volume usage)
            var volumes = await _dockerClient.Volumes.ListAsync(cancellationToken);
            var volumesSize = 0L; // Volume size calculation would require additional API calls

            return new DockerDiskUsage(
                ImagesSize: imagesSize,
                ContainersSize: containersSize,
                VolumesSize: volumesSize,
                BuildCacheSize: 0L // Build cache size not easily available via Docker.DotNet
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get disk usage: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Clean up unused Docker resources
    /// </summary>
    public async Task<CleanupResult> CleanupUnusedResourcesAsync(bool includeVolumes = false, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting Docker cleanup (IncludeVolumes: {IncludeVolumes})", includeVolumes);

            var imagesDeleted = 0;
            var containersDeleted = 0;
            var volumesDeleted = 0;
            var networksDeleted = 0;
            var spaceReclaimed = 0L;

            // Prune containers
            var containerPrune = await _dockerClient.Containers.PruneContainersAsync(new ContainersPruneParameters(), cancellationToken);
            containersDeleted = containerPrune.ContainersDeleted?.Count ?? 0;
            spaceReclaimed += (long)containerPrune.SpaceReclaimed;

            // Prune images
            var imagePrune = await _dockerClient.Images.PruneImagesAsync(new ImagesPruneParameters(), cancellationToken);
            imagesDeleted = imagePrune.ImagesDeleted?.Count ?? 0;
            spaceReclaimed += (long)imagePrune.SpaceReclaimed;

            // Prune networks
            var networkPrune = await _dockerClient.Networks.PruneNetworksAsync(new NetworksDeleteUnusedParameters(), cancellationToken);
            networksDeleted = networkPrune.NetworksDeleted?.Count ?? 0;

            // Prune volumes if requested
            if (includeVolumes)
            {
                var volumePrune = await _dockerClient.Volumes.PruneAsync(new VolumesPruneParameters(), cancellationToken);
                volumesDeleted = volumePrune.VolumesDeleted?.Count ?? 0;
                spaceReclaimed += (long)volumePrune.SpaceReclaimed;
            }

            _logger.LogInformation("Docker cleanup completed: {ImagesDeleted} images, {ContainersDeleted} containers, {NetworksDeleted} networks, {VolumesDeleted} volumes, {SpaceReclaimed} bytes reclaimed",
                imagesDeleted, containersDeleted, networksDeleted, volumesDeleted, spaceReclaimed);

            return new CleanupResult(
                ImagesDeleted: imagesDeleted,
                ContainersDeleted: containersDeleted,
                VolumesDeleted: volumesDeleted,
                NetworksDeleted: networksDeleted,
                SpaceReclaimed: spaceReclaimed
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup Docker resources: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Dispose the Docker client
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _dockerClient?.Dispose();
            _disposed = true;
            _logger.LogDebug("Docker client disposed");
        }
    }
}
