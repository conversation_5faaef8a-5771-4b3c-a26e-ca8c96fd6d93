using ReactiveUI;

namespace AutoInstaller.UI.ViewModels;

/// <summary>
/// View model for Docker system information
/// </summary>
public sealed class DockerSystemViewModel : ReactiveObject
{
    /// <summary>
    /// Whether Docker is available and running
    /// </summary>
    private bool _isAvailable;
    public bool IsAvailable
    {
        get => _isAvailable;
        set => this.RaiseAndSetIfChanged(ref _isAvailable, value);
    }

    /// <summary>
    /// Docker version
    /// </summary>
    private string _version = string.Empty;
    public string Version
    {
        get => _version;
        set => this.RaiseAndSetIfChanged(ref _version, value);
    }

    /// <summary>
    /// API version
    /// </summary>
    private string _apiVersion = string.Empty;
    public string ApiVersion
    {
        get => _apiVersion;
        set => this.RaiseAndSetIfChanged(ref _apiVersion, value);
    }

    /// <summary>
    /// Number of running containers
    /// </summary>
    private int _containersRunning;
    public int ContainersRunning
    {
        get => _containersRunning;
        set => this.RaiseAndSetIfChanged(ref _containersRunning, value);
    }

    /// <summary>
    /// Number of paused containers
    /// </summary>
    private int _containersPaused;
    public int ContainersPaused
    {
        get => _containersPaused;
        set => this.RaiseAndSetIfChanged(ref _containersPaused, value);
    }

    /// <summary>
    /// Number of stopped containers
    /// </summary>
    private int _containersStopped;
    public int ContainersStopped
    {
        get => _containersStopped;
        set => this.RaiseAndSetIfChanged(ref _containersStopped, value);
    }

    /// <summary>
    /// Total number of containers
    /// </summary>
    public int ContainersTotal => ContainersRunning + ContainersPaused + ContainersStopped;

    /// <summary>
    /// Number of images
    /// </summary>
    private int _imagesCount;
    public int ImagesCount
    {
        get => _imagesCount;
        set => this.RaiseAndSetIfChanged(ref _imagesCount, value);
    }

    /// <summary>
    /// Server version
    /// </summary>
    private string _serverVersion = string.Empty;
    public string ServerVersion
    {
        get => _serverVersion;
        set => this.RaiseAndSetIfChanged(ref _serverVersion, value);
    }

    /// <summary>
    /// Storage driver
    /// </summary>
    private string _storageDriver = string.Empty;
    public string StorageDriver
    {
        get => _storageDriver;
        set => this.RaiseAndSetIfChanged(ref _storageDriver, value);
    }

    /// <summary>
    /// Total memory in bytes
    /// </summary>
    private long _totalMemory;
    public long TotalMemory
    {
        get => _totalMemory;
        set => this.RaiseAndSetIfChanged(ref _totalMemory, value);
    }

    /// <summary>
    /// Number of CPUs
    /// </summary>
    private int _cpuCount;
    public int CpuCount
    {
        get => _cpuCount;
        set => this.RaiseAndSetIfChanged(ref _cpuCount, value);
    }

    /// <summary>
    /// Operating system
    /// </summary>
    private string _operatingSystem = string.Empty;
    public string OperatingSystem
    {
        get => _operatingSystem;
        set => this.RaiseAndSetIfChanged(ref _operatingSystem, value);
    }

    /// <summary>
    /// Architecture
    /// </summary>
    private string _architecture = string.Empty;
    public string Architecture
    {
        get => _architecture;
        set => this.RaiseAndSetIfChanged(ref _architecture, value);
    }

    /// <summary>
    /// Last check timestamp
    /// </summary>
    private DateTime _lastChecked = DateTime.UtcNow;
    public DateTime LastChecked
    {
        get => _lastChecked;
        set => this.RaiseAndSetIfChanged(ref _lastChecked, value);
    }

    /// <summary>
    /// Status display text
    /// </summary>
    public string StatusText => IsAvailable ? "Conectado" : "Desconectado";

    /// <summary>
    /// Status color for UI
    /// </summary>
    public string StatusColor => IsAvailable ? "#00D084" : "#E74C3C";

    /// <summary>
    /// Status icon
    /// </summary>
    public string StatusIcon => IsAvailable ? "✅" : "❌";

    /// <summary>
    /// Get formatted total memory
    /// </summary>
    /// <returns>Formatted memory string</returns>
    public string GetFormattedTotalMemory()
    {
        const long KB = 1024;
        const long MB = KB * 1024;
        const long GB = MB * 1024;

        return TotalMemory switch
        {
            < KB => $"{TotalMemory} B",
            < MB => $"{TotalMemory / (double)KB:F1} KB",
            < GB => $"{TotalMemory / (double)MB:F1} MB",
            _ => $"{TotalMemory / (double)GB:F1} GB"
        };
    }

    /// <summary>
    /// Get formatted last checked time
    /// </summary>
    /// <returns>Formatted last checked time</returns>
    public string GetFormattedLastChecked()
    {
        var timeSpan = DateTime.UtcNow - LastChecked;
        
        return timeSpan.TotalMinutes switch
        {
            < 1 => "Agora mesmo",
            < 60 => $"{(int)timeSpan.TotalMinutes} minuto(s) atrás",
            < 1440 => $"{(int)timeSpan.TotalHours} hora(s) atrás",
            _ => $"{(int)timeSpan.TotalDays} dia(s) atrás"
        };
    }

    /// <summary>
    /// Get system health summary
    /// </summary>
    /// <returns>Health summary text</returns>
    public string GetHealthSummary()
    {
        if (!IsAvailable)
            return "Docker não está disponível";

        var issues = new List<string>();

        if (ContainersRunning == 0 && ContainersTotal > 0)
            issues.Add("Nenhum container em execução");

        if (ImagesCount == 0)
            issues.Add("Nenhuma imagem disponível");

        if (TotalMemory < 1024 * 1024 * 1024) // Less than 1GB
            issues.Add("Pouca memória disponível");

        return issues.Any() 
            ? $"Atenção: {string.Join(", ", issues)}"
            : "Sistema funcionando normalmente";
    }

    /// <summary>
    /// Get containers summary text
    /// </summary>
    /// <returns>Containers summary</returns>
    public string GetContainersSummary()
    {
        if (ContainersTotal == 0)
            return "Nenhum container";

        var parts = new List<string>();

        if (ContainersRunning > 0)
            parts.Add($"{ContainersRunning} executando");

        if (ContainersPaused > 0)
            parts.Add($"{ContainersPaused} pausado(s)");

        if (ContainersStopped > 0)
            parts.Add($"{ContainersStopped} parado(s)");

        return $"{ContainersTotal} total ({string.Join(", ", parts)})";
    }

    /// <summary>
    /// Get system specifications summary
    /// </summary>
    /// <returns>System specs summary</returns>
    public string GetSystemSpecsSummary()
    {
        var specs = new List<string>();

        if (CpuCount > 0)
            specs.Add($"{CpuCount} CPU(s)");

        if (TotalMemory > 0)
            specs.Add(GetFormattedTotalMemory());

        if (!string.IsNullOrEmpty(StorageDriver))
            specs.Add($"Driver: {StorageDriver}");

        return specs.Any() ? string.Join(" • ", specs) : "Especificações não disponíveis";
    }
}
