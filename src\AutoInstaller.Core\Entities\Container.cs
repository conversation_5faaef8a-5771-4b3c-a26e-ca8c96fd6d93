using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Events;
using System.Collections.Immutable;

namespace AutoInstaller.Core.Entities;

/// <summary>
/// Represents a Docker container entity in the domain
/// </summary>
public class Container
{
    private readonly List<DomainEvent> _domainEvents = new();
    private readonly List<PortMapping> _portMappings = new();
    private readonly List<VolumeMount> _volumeMounts = new();
    private readonly List<EnvironmentVariable> _environmentVariables = new();

    /// <summary>
    /// Initialize a new container
    /// </summary>
    /// <param name="id">Container identifier</param>
    /// <param name="name">Container name</param>
    /// <param name="imageTag">Docker image tag</param>
    /// <param name="status">Container status</param>
    private Container(ContainerId id, string name, ImageTag imageTag, ContainerStatus status)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Name = !string.IsNullOrWhiteSpace(name) ? name : throw new ArgumentException("Container name cannot be empty", nameof(name));
        ImageTag = imageTag ?? throw new ArgumentNullException(nameof(imageTag));
        Status = status;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Container unique identifier
    /// </summary>
    public ContainerId Id { get; private set; }

    /// <summary>
    /// Container name
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// Docker image tag
    /// </summary>
    public ImageTag ImageTag { get; private set; }

    /// <summary>
    /// Current container status
    /// </summary>
    public ContainerStatus Status { get; private set; }

    /// <summary>
    /// Container creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; private set; }

    /// <summary>
    /// Container description
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Port mappings for the container
    /// </summary>
    public IReadOnlyList<PortMapping> PortMappings => _portMappings.AsReadOnly();

    /// <summary>
    /// Volume mounts for the container
    /// </summary>
    public IReadOnlyList<VolumeMount> VolumeMounts => _volumeMounts.AsReadOnly();

    /// <summary>
    /// Environment variables for the container
    /// </summary>
    public IReadOnlyList<EnvironmentVariable> EnvironmentVariables => _environmentVariables.AsReadOnly();

    /// <summary>
    /// Domain events raised by this entity
    /// </summary>
    public IReadOnlyList<DomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Create a new container
    /// </summary>
    /// <param name="name">Container name</param>
    /// <param name="imageTag">Docker image tag</param>
    /// <param name="description">Optional description</param>
    /// <returns>New container instance</returns>
    public static Container Create(string name, ImageTag imageTag, string? description = null)
    {
        var containerId = ContainerId.New();
        return Create(containerId, name, imageTag, description);
    }

    /// <summary>
    /// Create a container with specific ID
    /// </summary>
    /// <param name="id">Container ID</param>
    /// <param name="name">Container name</param>
    /// <param name="imageTag">Docker image tag</param>
    /// <param name="description">Container description</param>
    /// <returns>New container instance</returns>
    public static Container Create(ContainerId id, string name, ImageTag imageTag, string? description = null)
    {
        var container = new Container(
            id,
            name,
            imageTag,
            ContainerStatus.Created
        );

        if (!string.IsNullOrWhiteSpace(description))
        {
            container.Description = description;
        }

        container.AddDomainEvent(new ContainerCreatedEvent(container.Id, container.Name, container.ImageTag));
        return container;
    }

    /// <summary>
    /// Start the container
    /// </summary>
    public void Start()
    {
        if (Status == ContainerStatus.Running)
            throw new InvalidOperationException("Container is already running");

        if (Status == ContainerStatus.Removed)
            throw new InvalidOperationException("Cannot start a removed container");

        Status = ContainerStatus.Running;
        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new ContainerStartedEvent(Id, Name));
    }

    /// <summary>
    /// Stop the container
    /// </summary>
    public void Stop()
    {
        if (Status != ContainerStatus.Running)
            throw new InvalidOperationException("Container is not running");

        Status = ContainerStatus.Stopped;
        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new ContainerStoppedEvent(Id, Name));
    }

    /// <summary>
    /// Remove the container
    /// </summary>
    public void Remove()
    {
        if (Status == ContainerStatus.Running)
            throw new InvalidOperationException("Cannot remove a running container. Stop it first.");

        Status = ContainerStatus.Removed;
        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new ContainerRemovedEvent(Id, Name));
    }

    /// <summary>
    /// Pause the container
    /// </summary>
    public void Pause()
    {
        if (Status != ContainerStatus.Running)
        {
            throw new InvalidOperationException("Can only pause a running container");
        }

        var previousStatus = Status;
        Status = ContainerStatus.Paused;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContainerStatusChangedEvent(Id, Name, previousStatus, Status));
    }

    /// <summary>
    /// Unpause the container
    /// </summary>
    public void Unpause()
    {
        if (Status != ContainerStatus.Paused)
        {
            throw new InvalidOperationException("Can only unpause a paused container");
        }

        var previousStatus = Status;
        Status = ContainerStatus.Running;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContainerStatusChangedEvent(Id, Name, previousStatus, Status));
    }

    /// <summary>
    /// Update container ID (used when Docker assigns actual ID)
    /// </summary>
    /// <param name="newId">New container ID</param>
    public void UpdateId(ContainerId newId)
    {
        if (newId == null)
            throw new ArgumentNullException(nameof(newId));

        Id = newId;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Update container status
    /// </summary>
    /// <param name="newStatus">New status</param>
    public void UpdateStatus(ContainerStatus newStatus)
    {
        if (Status == newStatus) return;

        var previousStatus = Status;
        Status = newStatus;
        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new ContainerStatusChangedEvent(Id, Name, previousStatus, newStatus));
    }

    /// <summary>
    /// Add port mapping to container
    /// </summary>
    /// <param name="portMapping">Port mapping to add</param>
    public void AddPortMapping(PortMapping portMapping)
    {
        ArgumentNullException.ThrowIfNull(portMapping);

        if (_portMappings.Any(p => p.HostPort == portMapping.HostPort))
            throw new InvalidOperationException($"Host port {portMapping.HostPort} is already mapped");

        _portMappings.Add(portMapping);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Remove port mapping from container
    /// </summary>
    /// <param name="hostPort">Host port to remove</param>
    public void RemovePortMapping(int hostPort)
    {
        var mapping = _portMappings.FirstOrDefault(p => p.HostPort == hostPort);
        if (mapping != null)
        {
            _portMappings.Remove(mapping);
            UpdatedAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Add volume mount to container
    /// </summary>
    /// <param name="volumeMount">Volume mount to add</param>
    public void AddVolumeMount(VolumeMount volumeMount)
    {
        ArgumentNullException.ThrowIfNull(volumeMount);

        if (_volumeMounts.Any(v => v.ContainerPath == volumeMount.ContainerPath))
            throw new InvalidOperationException($"Container path {volumeMount.ContainerPath} is already mounted");

        _volumeMounts.Add(volumeMount);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Remove volume mount from container
    /// </summary>
    /// <param name="containerPath">Container path to remove</param>
    public void RemoveVolumeMount(string containerPath)
    {
        var mount = _volumeMounts.FirstOrDefault(v => v.ContainerPath == containerPath);
        if (mount != null)
        {
            _volumeMounts.Remove(mount);
            UpdatedAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Add environment variable to container
    /// </summary>
    /// <param name="environmentVariable">Environment variable to add</param>
    public void AddEnvironmentVariable(EnvironmentVariable environmentVariable)
    {
        ArgumentNullException.ThrowIfNull(environmentVariable);

        var existing = _environmentVariables.FirstOrDefault(e => e.Name == environmentVariable.Name);
        if (existing != null)
        {
            _environmentVariables.Remove(existing);
        }

        _environmentVariables.Add(environmentVariable);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Remove environment variable from container
    /// </summary>
    /// <param name="name">Environment variable name to remove</param>
    public void RemoveEnvironmentVariable(string name)
    {
        var variable = _environmentVariables.FirstOrDefault(e => e.Name == name);
        if (variable != null)
        {
            _environmentVariables.Remove(variable);
            UpdatedAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Clear all domain events
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Add domain event
    /// </summary>
    /// <param name="domainEvent">Domain event to add</param>
    private void AddDomainEvent(DomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
}
