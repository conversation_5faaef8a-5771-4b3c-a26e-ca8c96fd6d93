namespace AutoInstaller.Infrastructure.Configuration;

/// <summary>
/// Docker configuration settings
/// </summary>
public sealed class DockerConfiguration
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "Docker";

    /// <summary>
    /// Docker API URI
    /// </summary>
    public string DockerApiUri { get; set; } = GetDefaultDockerUri();

    /// <summary>
    /// Connection timeout in seconds
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int RequestTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// Maximum retry attempts for failed operations
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Retry delay in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Enable Docker events monitoring
    /// </summary>
    public bool EnableEventsMonitoring { get; set; } = true;

    /// <summary>
    /// Docker registry settings
    /// </summary>
    public DockerRegistryConfiguration Registry { get; set; } = new();

    /// <summary>
    /// Container default settings
    /// </summary>
    public ContainerDefaultsConfiguration ContainerDefaults { get; set; } = new();

    /// <summary>
    /// Get default Docker URI based on platform
    /// </summary>
    /// <returns>Default Docker URI</returns>
    private static string GetDefaultDockerUri()
    {
        if (OperatingSystem.IsWindows())
        {
            return "npipe://./pipe/docker_engine";
        }
        else if (OperatingSystem.IsLinux() || OperatingSystem.IsMacOS())
        {
            return "unix:///var/run/docker.sock";
        }
        else
        {
            return "tcp://localhost:2375";
        }
    }
}

/// <summary>
/// Docker registry configuration
/// </summary>
public sealed class DockerRegistryConfiguration
{
    /// <summary>
    /// Default registry URL (Docker Hub)
    /// </summary>
    public string DefaultRegistry { get; set; } = "https://index.docker.io/v1/";

    /// <summary>
    /// Registry authentication settings
    /// </summary>
    public Dictionary<string, RegistryAuthConfiguration> Registries { get; set; } = new();

    /// <summary>
    /// Enable automatic image pulling
    /// </summary>
    public bool AutoPullImages { get; set; } = true;

    /// <summary>
    /// Pull timeout in seconds
    /// </summary>
    public int PullTimeoutSeconds { get; set; } = 600;
}

/// <summary>
/// Registry authentication configuration
/// </summary>
public sealed class RegistryAuthConfiguration
{
    /// <summary>
    /// Registry server address
    /// </summary>
    public required string ServerAddress { get; set; }

    /// <summary>
    /// Username
    /// </summary>
    public required string Username { get; set; }

    /// <summary>
    /// Password or token
    /// </summary>
    public required string Password { get; set; }

    /// <summary>
    /// Email (optional)
    /// </summary>
    public string? Email { get; set; }
}

/// <summary>
/// Container default configuration
/// </summary>
public sealed class ContainerDefaultsConfiguration
{
    /// <summary>
    /// Default restart policy
    /// </summary>
    public string RestartPolicy { get; set; } = "unless-stopped";

    /// <summary>
    /// Default network mode
    /// </summary>
    public string NetworkMode { get; set; } = "bridge";

    /// <summary>
    /// Default memory limit in bytes (0 = no limit)
    /// </summary>
    public long MemoryLimit { get; set; } = 0;

    /// <summary>
    /// Default CPU limit (0 = no limit)
    /// </summary>
    public double CpuLimit { get; set; } = 0;

    /// <summary>
    /// Default environment variables
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// Default labels
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new()
    {
        ["created-by"] = "auto-installer",
        ["managed"] = "true"
    };

    /// <summary>
    /// Enable logging by default
    /// </summary>
    public bool EnableLogging { get; set; } = true;

    /// <summary>
    /// Default log driver
    /// </summary>
    public string LogDriver { get; set; } = "json-file";

    /// <summary>
    /// Default log options
    /// </summary>
    public Dictionary<string, string> LogOptions { get; set; } = new()
    {
        ["max-size"] = "10m",
        ["max-file"] = "3"
    };
}

/// <summary>
/// Docker monitoring configuration
/// </summary>
public sealed class DockerMonitoringConfiguration
{
    /// <summary>
    /// Enable container health monitoring
    /// </summary>
    public bool EnableHealthMonitoring { get; set; } = true;

    /// <summary>
    /// Health check interval in seconds
    /// </summary>
    public int HealthCheckIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// Enable resource usage monitoring
    /// </summary>
    public bool EnableResourceMonitoring { get; set; } = true;

    /// <summary>
    /// Resource monitoring interval in seconds
    /// </summary>
    public int ResourceMonitoringIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// Enable event monitoring
    /// </summary>
    public bool EnableEventMonitoring { get; set; } = true;

    /// <summary>
    /// Event buffer size
    /// </summary>
    public int EventBufferSize { get; set; } = 1000;
}

/// <summary>
/// Docker security configuration
/// </summary>
public sealed class DockerSecurityConfiguration
{
    /// <summary>
    /// Enable TLS verification
    /// </summary>
    public bool EnableTlsVerification { get; set; } = true;

    /// <summary>
    /// TLS certificate path
    /// </summary>
    public string? TlsCertPath { get; set; }

    /// <summary>
    /// TLS key path
    /// </summary>
    public string? TlsKeyPath { get; set; }

    /// <summary>
    /// TLS CA certificate path
    /// </summary>
    public string? TlsCaPath { get; set; }

    /// <summary>
    /// Allowed image registries
    /// </summary>
    public List<string> AllowedRegistries { get; set; } = new()
    {
        "docker.io",
        "registry-1.docker.io",
        "index.docker.io"
    };

    /// <summary>
    /// Blocked image patterns
    /// </summary>
    public List<string> BlockedImagePatterns { get; set; } = new();

    /// <summary>
    /// Enable image vulnerability scanning
    /// </summary>
    public bool EnableVulnerabilityScanning { get; set; } = false;

    /// <summary>
    /// Maximum allowed vulnerability severity
    /// </summary>
    public string MaxAllowedVulnerabilitySeverity { get; set; } = "HIGH";
}
