using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Models;

/// <summary>
/// Container information for listing operations
/// </summary>
public class ContainerInfo
{
    /// <summary>
    /// Container ID
    /// </summary>
    public ContainerId Id { get; init; } = null!;

    /// <summary>
    /// Container name
    /// </summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>
    /// Image tag used by the container
    /// </summary>
    public ImageTag ImageTag { get; init; } = null!;

    /// <summary>
    /// Current container status
    /// </summary>
    public ContainerStatus Status { get; init; }

    /// <summary>
    /// Detailed status description
    /// </summary>
    public string StatusDescription { get; init; } = string.Empty;
}
