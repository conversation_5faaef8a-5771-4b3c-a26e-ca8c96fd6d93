namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker container identifier
/// </summary>
public sealed record ContainerId
{
    /// <summary>
    /// Container ID value
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Initialize a new container ID
    /// </summary>
    /// <param name="value">Container ID value</param>
    private ContainerId(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Create a new container ID from string value
    /// </summary>
    /// <param name="value">Container ID string</param>
    /// <returns>Container ID instance</returns>
    /// <exception cref="ArgumentException">Thrown when value is invalid</exception>
    public static ContainerId From(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Container ID cannot be null or empty", nameof(value));

        if (value.Length < 12)
            throw new ArgumentException("Container ID must be at least 12 characters long", nameof(value));

        if (value.Length > 64)
            throw new ArgumentException("Container ID cannot be longer than 64 characters", nameof(value));

        if (!IsValidHexString(value))
            throw new ArgumentException("Container ID must contain only hexadecimal characters", nameof(value));

        return new ContainerId(value.ToLowerInvariant());
    }

    /// <summary>
    /// Generate a new unique container ID
    /// </summary>
    /// <returns>New container ID instance</returns>
    public static ContainerId New()
    {
        // Generate a 64-character hexadecimal string similar to Docker container IDs
        var guid1 = Guid.NewGuid().ToString("N");
        var guid2 = Guid.NewGuid().ToString("N");
        var containerIdValue = (guid1 + guid2)[..64];
        
        return new ContainerId(containerIdValue);
    }

    /// <summary>
    /// Get short version of container ID (first 12 characters)
    /// </summary>
    /// <returns>Short container ID</returns>
    public string GetShortId()
    {
        return Value[..12];
    }

    /// <summary>
    /// Check if this is a short container ID
    /// </summary>
    /// <returns>True if ID is 12 characters or less</returns>
    public bool IsShortId()
    {
        return Value.Length <= 12;
    }

    /// <summary>
    /// Check if this is a full container ID
    /// </summary>
    /// <returns>True if ID is longer than 12 characters</returns>
    public bool IsFullId()
    {
        return Value.Length > 12;
    }

    /// <summary>
    /// Validate if string contains only hexadecimal characters
    /// </summary>
    /// <param name="value">String to validate</param>
    /// <returns>True if string is valid hexadecimal</returns>
    private static bool IsValidHexString(string value)
    {
        return value.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    /// <summary>
    /// Implicit conversion from string to ContainerId
    /// </summary>
    /// <param name="value">String value</param>
    public static implicit operator ContainerId(string value) => From(value);

    /// <summary>
    /// Implicit conversion from ContainerId to string
    /// </summary>
    /// <param name="containerId">Container ID</param>
    public static implicit operator string(ContainerId containerId) => containerId.Value;

    /// <summary>
    /// String representation of container ID
    /// </summary>
    /// <returns>Container ID value</returns>
    public override string ToString() => Value;
}
