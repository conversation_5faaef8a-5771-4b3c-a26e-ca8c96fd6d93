namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Enumeration representing Docker container status
/// </summary>
public enum ContainerStatus
{
    /// <summary>
    /// Container has been created but not started
    /// </summary>
    Created,

    /// <summary>
    /// Container is currently running
    /// </summary>
    Running,

    /// <summary>
    /// Container is paused
    /// </summary>
    Paused,

    /// <summary>
    /// Container is restarting
    /// </summary>
    Restarting,

    /// <summary>
    /// Container has been stopped
    /// </summary>
    Stopped,

    /// <summary>
    /// Container has been removed
    /// </summary>
    Removed,

    /// <summary>
    /// Container is being removed
    /// </summary>
    Removing,

    /// <summary>
    /// Container is dead (cannot be restarted)
    /// </summary>
    Dead,

    /// <summary>
    /// Container has exited (stopped with exit code)
    /// </summary>
    Exited,

    /// <summary>
    /// Container is in an unknown state
    /// </summary>
    Unknown
}

/// <summary>
/// Extension methods for ContainerStatus
/// </summary>
public static class ContainerStatusExtensions
{
    /// <summary>
    /// Check if container is in a running state
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>True if container is running</returns>
    public static bool IsRunning(this ContainerStatus status)
    {
        return status == ContainerStatus.Running;
    }

    /// <summary>
    /// Check if container is in a stopped state
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>True if container is stopped</returns>
    public static bool IsStopped(this ContainerStatus status)
    {
        return status is ContainerStatus.Stopped or ContainerStatus.Exited or ContainerStatus.Dead;
    }

    /// <summary>
    /// Check if container can be started
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>True if container can be started</returns>
    public static bool CanStart(this ContainerStatus status)
    {
        return status is ContainerStatus.Created or ContainerStatus.Stopped or ContainerStatus.Exited;
    }

    /// <summary>
    /// Check if container can be stopped
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>True if container can be stopped</returns>
    public static bool CanStop(this ContainerStatus status)
    {
        return status is ContainerStatus.Running or ContainerStatus.Paused or ContainerStatus.Restarting;
    }

    /// <summary>
    /// Check if container can be removed
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>True if container can be removed</returns>
    public static bool CanRemove(this ContainerStatus status)
    {
        return status is not ContainerStatus.Running and not ContainerStatus.Paused and not ContainerStatus.Restarting and not ContainerStatus.Removing;
    }

    /// <summary>
    /// Check if container can be paused
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>True if container can be paused</returns>
    public static bool CanPause(this ContainerStatus status)
    {
        return status == ContainerStatus.Running;
    }

    /// <summary>
    /// Check if container can be unpaused
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>True if container can be unpaused</returns>
    public static bool CanUnpause(this ContainerStatus status)
    {
        return status == ContainerStatus.Paused;
    }

    /// <summary>
    /// Get display name for status
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>Human-readable status name</returns>
    public static string GetDisplayName(this ContainerStatus status)
    {
        return status switch
        {
            ContainerStatus.Created => "Criado",
            ContainerStatus.Running => "Executando",
            ContainerStatus.Paused => "Pausado",
            ContainerStatus.Restarting => "Reiniciando",
            ContainerStatus.Stopped => "Parado",
            ContainerStatus.Removed => "Removido",
            ContainerStatus.Removing => "Removendo",
            ContainerStatus.Dead => "Morto",
            ContainerStatus.Exited => "Finalizado",
            ContainerStatus.Unknown => "Desconhecido",
            _ => status.ToString()
        };
    }

    /// <summary>
    /// Get status color for UI display
    /// </summary>
    /// <param name="status">Container status</param>
    /// <returns>Color name or hex code</returns>
    public static string GetStatusColor(this ContainerStatus status)
    {
        return status switch
        {
            ContainerStatus.Created => "#FFA500", // Orange
            ContainerStatus.Running => "#00D084", // Green
            ContainerStatus.Paused => "#F1C40F", // Yellow
            ContainerStatus.Restarting => "#FF6B35", // Orange-Red
            ContainerStatus.Stopped => "#BDC3C7", // Light Gray
            ContainerStatus.Removed => "#E74C3C", // Red
            ContainerStatus.Removing => "#FF4757", // Bright Red
            ContainerStatus.Dead => "#2C2C54", // Dark Purple
            ContainerStatus.Exited => "#95A5A6", // Gray
            ContainerStatus.Unknown => "#34495E", // Dark Gray
            _ => "#34495E"
        };
    }

    /// <summary>
    /// Parse status from Docker API string
    /// </summary>
    /// <param name="dockerStatus">Docker status string</param>
    /// <returns>Container status enum</returns>
    public static ContainerStatus FromDockerString(string dockerStatus)
    {
        if (string.IsNullOrWhiteSpace(dockerStatus))
            return ContainerStatus.Unknown;

        var status = dockerStatus.ToLowerInvariant();
        
        return status switch
        {
            "created" => ContainerStatus.Created,
            "running" => ContainerStatus.Running,
            "paused" => ContainerStatus.Paused,
            "restarting" => ContainerStatus.Restarting,
            "removing" => ContainerStatus.Removing,
            "dead" => ContainerStatus.Dead,
            var s when s.StartsWith("exited") => ContainerStatus.Exited,
            var s when s.StartsWith("up") => ContainerStatus.Running,
            _ => ContainerStatus.Unknown
        };
    }
}
