using MediatR;

namespace AutoInstaller.Application.Commands;

/// <summary>
/// Command to pull a Docker image
/// </summary>
public sealed record PullImageCommand : IRequest<ImageOperationResult>
{
    /// <summary>
    /// Image tag to pull
    /// </summary>
    public required string ImageTag { get; init; }

    /// <summary>
    /// Registry authentication if required
    /// </summary>
    public RegistryAuthDto? Authentication { get; init; }

    /// <summary>
    /// Pull all tags for the repository
    /// </summary>
    public bool PullAllTags { get; init; } = false;
}

/// <summary>
/// Command to remove a Docker image
/// </summary>
public sealed record RemoveImageCommand : IRequest<ImageOperationResult>
{
    /// <summary>
    /// Image ID or tag
    /// </summary>
    public required string ImageIdOrTag { get; init; }

    /// <summary>
    /// Force removal even if containers are using it
    /// </summary>
    public bool Force { get; init; } = false;

    /// <summary>
    /// Remove untagged parent images
    /// </summary>
    public bool NoPrune { get; init; } = false;
}

/// <summary>
/// Command to tag a Docker image
/// </summary>
public sealed record TagImageCommand : IRequest<ImageOperationResult>
{
    /// <summary>
    /// Source image ID or tag
    /// </summary>
    public required string SourceImageIdOrTag { get; init; }

    /// <summary>
    /// Target repository
    /// </summary>
    public required string TargetRepository { get; init; }

    /// <summary>
    /// Target tag
    /// </summary>
    public required string TargetTag { get; init; }
}

/// <summary>
/// Command to prune unused images
/// </summary>
public sealed record PruneImagesCommand : IRequest<PruneResult>
{
    /// <summary>
    /// Remove all unused images, not just dangling ones
    /// </summary>
    public bool PruneAll { get; init; } = false;

    /// <summary>
    /// Filters to apply during pruning
    /// </summary>
    public IReadOnlyDictionary<string, string> Filters { get; init; } = new Dictionary<string, string>();
}

/// <summary>
/// Registry authentication DTO
/// </summary>
public sealed record RegistryAuthDto
{
    /// <summary>
    /// Registry server URL
    /// </summary>
    public required string ServerAddress { get; init; }

    /// <summary>
    /// Username
    /// </summary>
    public required string Username { get; init; }

    /// <summary>
    /// Password
    /// </summary>
    public required string Password { get; init; }

    /// <summary>
    /// Email (optional)
    /// </summary>
    public string? Email { get; init; }
}

/// <summary>
/// Result of image operation
/// </summary>
public sealed record ImageOperationResult
{
    /// <summary>
    /// Image ID
    /// </summary>
    public required string ImageId { get; init; }

    /// <summary>
    /// Image repository
    /// </summary>
    public required string Repository { get; init; }

    /// <summary>
    /// Image tag
    /// </summary>
    public required string Tag { get; init; }

    /// <summary>
    /// Operation that was performed
    /// </summary>
    public required string Operation { get; init; }

    /// <summary>
    /// Success indicator
    /// </summary>
    public required bool Success { get; init; }

    /// <summary>
    /// Error message if operation failed
    /// </summary>
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Operation timestamp
    /// </summary>
    public required DateTime Timestamp { get; init; }

    /// <summary>
    /// Additional operation details
    /// </summary>
    public string? Details { get; init; }
}

/// <summary>
/// Result of prune operation
/// </summary>
public sealed record PruneResult
{
    /// <summary>
    /// Number of items deleted
    /// </summary>
    public required int ItemsDeleted { get; init; }

    /// <summary>
    /// Space reclaimed in bytes
    /// </summary>
    public required long SpaceReclaimed { get; init; }

    /// <summary>
    /// Success indicator
    /// </summary>
    public required bool Success { get; init; }

    /// <summary>
    /// Error message if operation failed
    /// </summary>
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Operation timestamp
    /// </summary>
    public required DateTime Timestamp { get; init; }

    /// <summary>
    /// Formatted space reclaimed string
    /// </summary>
    public string FormattedSpaceReclaimed
    {
        get
        {
            const long KB = 1024;
            const long MB = KB * 1024;
            const long GB = MB * 1024;

            return SpaceReclaimed switch
            {
                < KB => $"{SpaceReclaimed} B",
                < MB => $"{SpaceReclaimed / (double)KB:F1} KB",
                < GB => $"{SpaceReclaimed / (double)MB:F1} MB",
                _ => $"{SpaceReclaimed / (double)GB:F1} GB"
            };
        }
    }
}
