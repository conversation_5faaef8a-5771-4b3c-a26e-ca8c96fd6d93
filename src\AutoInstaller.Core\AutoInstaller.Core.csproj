<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>AutoInstaller.Core</RootNamespace>
    <AssemblyName>AutoInstaller.Core</AssemblyName>
    <Description>Domain layer containing entities, value objects, and business rules for the Auto-Installer Desktop application.</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="System.Collections.Immutable" />
  </ItemGroup>

</Project>
