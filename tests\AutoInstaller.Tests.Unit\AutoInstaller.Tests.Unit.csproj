<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>AutoInstaller.Tests.Unit</RootNamespace>
    <AssemblyName>AutoInstaller.Tests.Unit</AssemblyName>
    <Description>Unit tests for all layers of the Auto-Installer Desktop application.</Description>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AutoInstaller.Core\AutoInstaller.Core.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Application\AutoInstaller.Application.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.UI\AutoInstaller.UI.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Shared\AutoInstaller.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Moq" />
    <PackageReference Include="AutoFixture" />
    <PackageReference Include="AutoFixture.Xunit2" />
    <PackageReference Include="AutoFixture.AutoMoq" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="coverlet.msbuild" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
  </ItemGroup>

</Project>
