<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>AutoInstaller.Integration.Tests</RootNamespace>
    <AssemblyName>AutoInstaller.Integration.Tests</AssemblyName>
    <Description>Integration tests for Docker operations, database interactions, and plugin system.</Description>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AutoInstaller.Core\AutoInstaller.Core.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Application\AutoInstaller.Application.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.UI\AutoInstaller.UI.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Shared\AutoInstaller.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Moq" />
    <PackageReference Include="AutoFixture" />
    <PackageReference Include="AutoFixture.Xunit2" />
    <PackageReference Include="AutoFixture.AutoMoq" />
    <PackageReference Include="Testcontainers" />
    <PackageReference Include="Testcontainers.PostgreSql" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="coverlet.msbuild" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
  </ItemGroup>

</Project>
