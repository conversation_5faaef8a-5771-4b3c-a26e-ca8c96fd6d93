using Xunit;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AutoInstaller.Infrastructure.Services;
using AutoInstaller.Infrastructure.Configuration;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Integration.Tests.Docker;

/// <summary>
/// Integration tests for Docker service configuration and dependency injection
/// These tests work without requiring Docker to be available
/// </summary>
public class DockerServiceConfigurationIntegrationTests
{
    [Fact]
    public void DockerService_ShouldBeRegisteredInDependencyInjection()
    {
        // Arrange
        var services = new ServiceCollection();
        
        // Add required services
        services.AddLogging();
        services.Configure<DockerConfiguration>(config =>
        {
            config.DockerApiUri = "npipe://./pipe/docker_engine";
            config.ConnectionTimeoutSeconds = 30;
            config.RequestTimeoutSeconds = 60;
            config.MaxRetryAttempts = 3;
            config.RetryDelayMs = 1000;
        });
        
        // Add Docker services (this should work without Docker being available)
        services.AddSingleton<DockerClientService>();
        services.AddScoped<DockerService>();
        
        var serviceProvider = services.BuildServiceProvider();

        // Act & Assert
        var dockerService = serviceProvider.GetService<DockerService>();
        dockerService.Should().NotBeNull();
        
        var dockerClientService = serviceProvider.GetService<DockerClientService>();
        dockerClientService.Should().NotBeNull();
    }

    [Fact]
    public void DockerConfiguration_ShouldHaveCorrectDefaultValues()
    {
        // Arrange
        var services = new ServiceCollection();
        services.Configure<DockerConfiguration>(config =>
        {
            config.DockerApiUri = "npipe://./pipe/docker_engine";
            config.ConnectionTimeoutSeconds = 30;
            config.RequestTimeoutSeconds = 60;
            config.MaxRetryAttempts = 3;
            config.RetryDelayMs = 1000;
        });
        
        var serviceProvider = services.BuildServiceProvider();
        var options = serviceProvider.GetRequiredService<IOptions<DockerConfiguration>>();

        // Act
        var config = options.Value;

        // Assert
        config.Should().NotBeNull();
        config.DockerApiUri.Should().Be("npipe://./pipe/docker_engine");
        config.ConnectionTimeoutSeconds.Should().Be(30);
        config.RequestTimeoutSeconds.Should().Be(60);
        config.MaxRetryAttempts.Should().Be(3);
        config.RetryDelayMs.Should().Be(1000);
    }

    [Fact]
    public void ImageTag_ShouldParseCorrectlyInIntegrationContext()
    {
        // Arrange & Act
        var imageTag1 = ImageTag.From("nginx:latest");
        var imageTag2 = ImageTag.From("redis:alpine");
        var imageTag3 = ImageTag.From("postgres:13");

        // Assert
        imageTag1.Repository.Should().Be("nginx");
        imageTag1.Tag.Should().Be("latest");
        imageTag1.Value.Should().Be("nginx:latest");

        imageTag2.Repository.Should().Be("redis");
        imageTag2.Tag.Should().Be("alpine");
        imageTag2.Value.Should().Be("redis:alpine");

        imageTag3.Repository.Should().Be("postgres");
        imageTag3.Tag.Should().Be("13");
        imageTag3.Value.Should().Be("postgres:13");
    }

    [Fact]
    public void ContainerId_ShouldValidateCorrectlyInIntegrationContext()
    {
        // Arrange
        var validId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var shortId = "a1b2c3d4e5f6";

        // Act & Assert
        var containerId1 = ContainerId.From(validId);
        containerId1.Value.Should().Be(validId.ToLowerInvariant());

        Action createWithShortId = () => ContainerId.From(shortId);
        createWithShortId.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void EnvironmentVariable_ShouldCreateCorrectlyInIntegrationContext()
    {
        // Arrange & Act
        var envVar1 = EnvironmentVariable.Create("DATABASE_URL", "postgresql://localhost:5432/mydb");
        var envVar2 = EnvironmentVariable.Create("API_KEY", "secret-key-123", isSensitive: true);
        var envVar3 = EnvironmentVariable.Create("PORT", "8080");

        // Assert
        envVar1.Name.Should().Be("DATABASE_URL");
        envVar1.Value.Should().Be("postgresql://localhost:5432/mydb");
        envVar1.IsSensitive.Should().BeFalse();

        envVar2.Name.Should().Be("API_KEY");
        envVar2.Value.Should().Be("secret-key-123");
        envVar2.IsSensitive.Should().BeTrue();

        envVar3.Name.Should().Be("PORT");
        envVar3.Value.Should().Be("8080");
        envVar3.IsSensitive.Should().BeFalse();
    }

    [Fact]
    public void DockerService_ShouldDisposeCorrectly()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();
        services.Configure<DockerConfiguration>(config =>
        {
            config.DockerApiUri = "npipe://./pipe/docker_engine";
            config.ConnectionTimeoutSeconds = 30;
            config.RequestTimeoutSeconds = 60;
            config.MaxRetryAttempts = 3;
            config.RetryDelayMs = 1000;
        });
        services.AddSingleton<DockerClientService>();
        services.AddScoped<DockerService>();
        
        var serviceProvider = services.BuildServiceProvider();
        var dockerService = serviceProvider.GetRequiredService<DockerService>();

        // Act & Assert - Should not throw
        Action dispose = () => dockerService.Dispose();
        dispose.Should().NotThrow();
    }

    [Fact]
    public void DockerClientService_ShouldCreateWithCorrectConfiguration()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();
        services.Configure<DockerConfiguration>(config =>
        {
            config.DockerApiUri = "npipe://./pipe/docker_engine";
            config.ConnectionTimeoutSeconds = 45;
            config.RequestTimeoutSeconds = 90;
            config.MaxRetryAttempts = 5;
            config.RetryDelayMs = 2000;
        });
        services.AddSingleton<DockerClientService>();
        
        var serviceProvider = services.BuildServiceProvider();

        // Act
        var dockerClientService = serviceProvider.GetRequiredService<DockerClientService>();

        // Assert
        dockerClientService.Should().NotBeNull();
        
        // Verify configuration was applied (indirectly through successful creation)
        Action dispose = () => dockerClientService.Dispose();
        dispose.Should().NotThrow();
    }

    [Theory]
    [InlineData("nginx:latest")]
    [InlineData("redis:alpine")]
    [InlineData("postgres:13")]
    [InlineData("ubuntu:20.04")]
    [InlineData("node:16-alpine")]
    public void ImageTag_ShouldParseVariousFormatsCorrectly(string imageTagString)
    {
        // Act
        var imageTag = ImageTag.From(imageTagString);

        // Assert
        imageTag.Should().NotBeNull();
        imageTag.Value.Should().Be(imageTagString);
        imageTag.Repository.Should().NotBeNullOrEmpty();
        imageTag.Tag.Should().NotBeNullOrEmpty();
    }

    [Theory]
    [InlineData("a1b2c3d4e5f6789012345678901234567890123456789012345678901234")]
    [InlineData("b2c3d4e5f6a789012345678901234567890123456789012345678901234")]
    [InlineData("c3d4e5f6b7a89012345678901234567890123456789012345678901234")]
    public void ContainerId_ShouldAcceptValidIds(string validId)
    {
        // Act
        var containerId = ContainerId.From(validId);

        // Assert
        containerId.Should().NotBeNull();
        containerId.Value.Should().Be(validId.ToLowerInvariant());
        containerId.Value.Length.Should().Be(64);
    }

    [Fact]
    public void Integration_ValueObjects_ShouldWorkTogetherCorrectly()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:alpine");
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");
        var envVars = new List<EnvironmentVariable>
        {
            EnvironmentVariable.Create("NGINX_PORT", "80"),
            EnvironmentVariable.Create("ENVIRONMENT", "production")
        };

        // Act & Assert - Should work together without issues
        imageTag.Should().NotBeNull();
        containerId.Should().NotBeNull();
        envVars.Should().HaveCount(2);
        
        // Verify they can be used in collections
        var containerInfo = new
        {
            Id = containerId,
            Image = imageTag,
            Environment = envVars
        };
        
        containerInfo.Should().NotBeNull();
        containerInfo.Id.Value.Length.Should().Be(64);
        containerInfo.Image.Value.Should().Contain(":");
        containerInfo.Environment.Should().AllSatisfy(env => 
        {
            env.Name.Should().NotBeNullOrEmpty();
            env.Value.Should().NotBeNullOrEmpty();
        });
    }
}
