using Docker.DotNet;
using Docker.DotNet.Models;
using Microsoft.Extensions.Logging;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces.Repositories;
using AutoInstaller.Core.ValueObjects;
using DomainContainerStatus = AutoInstaller.Core.ValueObjects.ContainerStatus;
using DockerContainerStatus = Docker.DotNet.Models.ContainerStatus;

namespace AutoInstaller.Infrastructure.Repositories;

/// <summary>
/// Container repository implementation using Docker.DotNet
/// </summary>
public sealed class ContainerRepository : IContainerRepository
{
    private readonly DockerClient _dockerClient;
    private readonly ILogger<ContainerRepository> _logger;

    /// <summary>
    /// Initialize the container repository
    /// </summary>
    /// <param name="dockerClient">Docker client</param>
    /// <param name="logger">Logger</param>
    public ContainerRepository(DockerClient dockerClient, ILogger<ContainerRepository> logger)
    {
        _dockerClient = dockerClient;
        _logger = logger;
    }

    /// <summary>
    /// Get container by ID
    /// </summary>
    /// <param name="id">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Container if found, null otherwise</returns>
    public async Task<Container?> GetByIdAsync(ContainerId id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting container by ID: {ContainerId}", id.Value);

            var containerResponse = await _dockerClient.Containers.InspectContainerAsync(id.Value, cancellationToken);
            
            return MapToContainer(containerResponse);
        }
        catch (DockerContainerNotFoundException)
        {
            _logger.LogDebug("Container not found: {ContainerId}", id.Value);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get container by ID {ContainerId}: {Error}", id.Value, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get container by name
    /// </summary>
    /// <param name="name">Container name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Container if found, null otherwise</returns>
    public async Task<Container?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting container by name: {ContainerName}", name);

            var containers = await _dockerClient.Containers.ListContainersAsync(
                new ContainersListParameters { All = true }, cancellationToken);

            var containerSummary = containers.FirstOrDefault(c => 
                c.Names.Any(n => n.TrimStart('/') == name));

            if (containerSummary == null)
            {
                _logger.LogDebug("Container not found: {ContainerName}", name);
                return null;
            }

            var containerResponse = await _dockerClient.Containers.InspectContainerAsync(
                containerSummary.ID, cancellationToken);
            
            return MapToContainer(containerResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get container by name {ContainerName}: {Error}", name, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get all containers
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all containers</returns>
    public async Task<IReadOnlyList<Container>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting all containers");

            var containers = await _dockerClient.Containers.ListContainersAsync(
                new ContainersListParameters { All = true }, cancellationToken);

            var result = new List<Container>();

            foreach (var containerSummary in containers)
            {
                try
                {
                    var containerResponse = await _dockerClient.Containers.InspectContainerAsync(
                        containerSummary.ID, cancellationToken);
                    
                    var container = MapToContainer(containerResponse);
                    if (container != null)
                    {
                        result.Add(container);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to inspect container {ContainerId}: {Error}", 
                        containerSummary.ID, ex.Message);
                }
            }

            return result.AsReadOnly();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all containers: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get containers by status
    /// </summary>
    /// <param name="status">Container status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of containers with specified status</returns>
    public async Task<IReadOnlyList<Container>> GetByStatusAsync(DomainContainerStatus status, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting containers by status: {Status}", status);

            var allContainers = await GetAllAsync(cancellationToken);
            
            return allContainers.Where(c => c.Status == status).ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get containers by status {Status}: {Error}", status, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get containers by image tag
    /// </summary>
    /// <param name="imageTag">Image tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of containers using specified image</returns>
    public async Task<IReadOnlyList<Container>> GetByImageTagAsync(ImageTag imageTag, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting containers by image tag: {ImageTag}", imageTag.Value);

            var allContainers = await GetAllAsync(cancellationToken);
            
            return allContainers.Where(c => c.ImageTag.Value == imageTag.Value).ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get containers by image tag {ImageTag}: {Error}", imageTag.Value, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get running containers
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of running containers</returns>
    public async Task<IReadOnlyList<Container>> GetRunningAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting running containers");

            var containers = await _dockerClient.Containers.ListContainersAsync(
                new ContainersListParameters { All = false }, cancellationToken);

            var result = new List<Container>();

            foreach (var containerSummary in containers)
            {
                try
                {
                    var containerResponse = await _dockerClient.Containers.InspectContainerAsync(
                        containerSummary.ID, cancellationToken);
                    
                    var container = MapToContainer(containerResponse);
                    if (container != null)
                    {
                        result.Add(container);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to inspect running container {ContainerId}: {Error}", 
                        containerSummary.ID, ex.Message);
                }
            }

            return result.AsReadOnly();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get running containers: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Check if container exists by ID
    /// </summary>
    /// <param name="id">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if container exists</returns>
    public async Task<bool> ExistsAsync(ContainerId id, CancellationToken cancellationToken = default)
    {
        try
        {
            var container = await GetByIdAsync(id, cancellationToken);
            return container != null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check if container exists {ContainerId}: {Error}", id.Value, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Check if container name is available
    /// </summary>
    /// <param name="name">Container name</param>
    /// <param name="excludeId">Container ID to exclude from check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if name is available</returns>
    public async Task<bool> IsNameAvailableAsync(string name, ContainerId? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var container = await GetByNameAsync(name, cancellationToken);
            
            if (container == null)
                return true;

            if (excludeId != null && container.Id.Value == excludeId.Value)
                return true;

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check if container name is available {ContainerName}: {Error}", name, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Add new container
    /// </summary>
    /// <param name="container">Container to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task AddAsync(Container container, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Creating container: {ContainerName}", container.Name);

            var createParameters = MapToCreateParameters(container);
            
            var response = await _dockerClient.Containers.CreateContainerAsync(
                createParameters, cancellationToken);

            // Update container with actual ID from Docker
            var actualId = ContainerId.From(response.ID);
            container.UpdateId(actualId);

            _logger.LogInformation("Container created successfully: {ContainerName} ({ContainerId})", 
                container.Name, container.Id.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create container {ContainerName}: {Error}", container.Name, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Update existing container
    /// </summary>
    /// <param name="container">Container to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public Task UpdateAsync(Container container, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Updating container: {ContainerName} ({ContainerId})",
                container.Name, container.Id.Value);

            // Docker containers are mostly immutable, so we mainly track state changes
            // The actual container state is managed through Docker API operations

            _logger.LogDebug("Container state updated: {ContainerName} -> {Status}",
                container.Name, container.Status);

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update container {ContainerName}: {Error}", container.Name, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Remove container
    /// </summary>
    /// <param name="container">Container to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task RemoveAsync(Container container, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Removing container: {ContainerName} ({ContainerId})", 
                container.Name, container.Id.Value);

            await _dockerClient.Containers.RemoveContainerAsync(
                container.Id.Value,
                new ContainerRemoveParameters { Force = true },
                cancellationToken);

            _logger.LogInformation("Container removed successfully: {ContainerName} ({ContainerId})", 
                container.Name, container.Id.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove container {ContainerName}: {Error}", container.Name, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Remove container by ID
    /// </summary>
    /// <param name="id">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task RemoveByIdAsync(ContainerId id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Removing container by ID: {ContainerId}", id.Value);

            await _dockerClient.Containers.RemoveContainerAsync(
                id.Value,
                new ContainerRemoveParameters { Force = true },
                cancellationToken);

            _logger.LogInformation("Container removed successfully: {ContainerId}", id.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove container by ID {ContainerId}: {Error}", id.Value, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get containers with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of containers</returns>
    public async Task<(IReadOnlyList<Container> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var allContainers = await GetAllAsync(cancellationToken);
            var totalCount = allContainers.Count;
            
            var items = allContainers
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToArray();

            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get paged containers: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Search containers by name pattern
    /// </summary>
    /// <param name="namePattern">Name pattern to search</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching containers</returns>
    public async Task<IReadOnlyList<Container>> SearchByNameAsync(string namePattern, CancellationToken cancellationToken = default)
    {
        try
        {
            var allContainers = await GetAllAsync(cancellationToken);
            
            return allContainers
                .Where(c => c.Name.Contains(namePattern, StringComparison.OrdinalIgnoreCase))
                .ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search containers by name pattern {Pattern}: {Error}", namePattern, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get container count
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total number of containers</returns>
    public async Task<int> GetCountAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var containers = await _dockerClient.Containers.ListContainersAsync(
                new ContainersListParameters { All = true }, cancellationToken);
            
            return containers.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get container count: {Error}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Get container count by status
    /// </summary>
    /// <param name="status">Container status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of containers with specified status</returns>
    public async Task<int> GetCountByStatusAsync(DomainContainerStatus status, CancellationToken cancellationToken = default)
    {
        try
        {
            var containers = await GetByStatusAsync(status, cancellationToken);
            return containers.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get container count by status {Status}: {Error}", status, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Map Docker container response to domain container
    /// </summary>
    /// <param name="containerResponse">Docker container response</param>
    /// <returns>Domain container</returns>
    private static Container? MapToContainer(ContainerInspectResponse containerResponse)
    {
        try
        {
            var containerId = ContainerId.From(containerResponse.ID);
            var name = containerResponse.Name.TrimStart('/');
            var imageTag = ImageTag.From(containerResponse.Config.Image);
            var status = ContainerStatusExtensions.FromDockerString(containerResponse.State.Status);

            var container = Container.Create(containerId, name, imageTag, null);

            // Update status based on Docker state
            switch (status)
            {
                case DomainContainerStatus.Running:
                    container.Start();
                    break;
                case DomainContainerStatus.Stopped:
                case DomainContainerStatus.Exited:
                    container.Stop();
                    break;
                case DomainContainerStatus.Paused:
                    container.Pause();
                    break;
            }

            return container;
        }
        catch (Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// Map domain container to Docker create parameters
    /// </summary>
    /// <param name="container">Domain container</param>
    /// <returns>Docker create parameters</returns>
    private static CreateContainerParameters MapToCreateParameters(Container container)
    {
        var config = new Config
        {
            Image = container.ImageTag.Value,
            Env = container.EnvironmentVariables.Select(env => env.ToDockerString()).ToList()
        };

        var hostConfig = new HostConfig
        {
            PortBindings = new Dictionary<string, IList<PortBinding>>(),
            Binds = container.VolumeMounts.Select(vm => vm.ToDockerString()).ToList()
        };

        // Map port bindings
        foreach (var portMapping in container.PortMappings)
        {
            var containerPort = $"{portMapping.ContainerPort}/{portMapping.Protocol.ToString().ToLowerInvariant()}";
            var portBinding = new PortBinding
            {
                HostPort = portMapping.HostPort.ToString(),
                HostIP = portMapping.HostIP ?? "0.0.0.0"
            };

            if (!hostConfig.PortBindings.ContainsKey(containerPort))
            {
                hostConfig.PortBindings[containerPort] = new List<PortBinding>();
            }
            hostConfig.PortBindings[containerPort].Add(portBinding);
        }

        return new CreateContainerParameters(config)
        {
            Name = container.Name,
            HostConfig = hostConfig
        };
    }
}
