using Xunit;
using FluentAssertions;
using Docker.DotNet;
using Docker.DotNet.Models;
using AutoInstaller.Infrastructure.Services;
using AutoInstaller.Core.ValueObjects;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DotNet.Testcontainers.Builders;
using DotNet.Testcontainers.Configurations;

namespace AutoInstaller.Integration.Tests.Docker;

/// <summary>
/// Real integration tests for Docker service operations using TestContainers
/// Tests against real Docker daemon with real containers
/// </summary>
public class DockerServiceIntegrationTests : IAsyncLifetime
{
    private readonly ILogger<DockerService> _logger;
    private readonly DockerService _dockerService;
    private readonly IDockerClient _dockerClient;
    private readonly List<string> _createdContainerIds;

    public DockerServiceIntegrationTests()
    {
        // Setup real logger
        var serviceProvider = new ServiceCollection()
            .AddLogging(builder => builder.AddConsole())
            .BuildServiceProvider();

        _logger = serviceProvider.GetRequiredService<ILogger<DockerService>>();

        // Create real Docker client
        _dockerClient = new DockerClientConfiguration().CreateClient();

        // Create real Docker service
        _dockerService = new DockerService(_dockerClient, _logger);

        // Track created containers for cleanup
        _createdContainerIds = new List<string>();
    }

    [Fact]
    public async Task CreateContainerAsync_WithValidParameters_ShouldCreateRealContainer()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:alpine"); // Use lightweight image for faster tests
        var containerName = $"test-nginx-{Guid.NewGuid():N}"; // Unique name to avoid conflicts

        try
        {
            // Ensure image exists (pull if needed)
            await EnsureImageExistsAsync(imageTag);

            // Act
            var result = await _dockerService.CreateContainerAsync(imageTag, containerName);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNullOrEmpty();
            result.Value.Length.Should().Be(64); // Docker container ID should be 64 characters

            // Track for cleanup
            _createdContainerIds.Add(result.Value);

            // Verify container actually exists in Docker
            var containerExists = await ContainerExistsAsync(result.Value);
            containerExists.Should().BeTrue("Container should exist in Docker after creation");

            // Verify container name
            var containerInfo = await _dockerClient.Containers.InspectContainerAsync(result.Value);
            containerInfo.Name.Should().Be($"/{containerName}");
            containerInfo.Config.Image.Should().Be(imageTag.Value);
        }
        catch (Exception)
        {
            // Cleanup on failure
            if (!string.IsNullOrEmpty(containerName))
            {
                await CleanupContainerByNameAsync(containerName);
            }
            throw;
        }
    }

    [Fact]
    public async Task StartContainerAsync_WithValidContainerId_ShouldStartRealContainer()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:alpine");
        var containerName = $"test-start-{Guid.NewGuid():N}";

        try
        {
            // Ensure image exists
            await EnsureImageExistsAsync(imageTag);

            // Create container first
            var createResult = await _dockerService.CreateContainerAsync(imageTag, containerName);
            createResult.Should().NotBeNull();

            var containerId = ContainerId.From(createResult.Value);
            _createdContainerIds.Add(createResult.Value);

            // Verify container is initially stopped
            var initialStatus = await _dockerService.GetContainerStatusAsync(containerId);
            initialStatus.Should().Be(Core.ValueObjects.ContainerStatus.Stopped);

            // Act
            var result = await _dockerService.StartContainerAsync(containerId);

            // Assert
            result.Should().BeTrue();

            // Verify container is now running
            var finalStatus = await _dockerService.GetContainerStatusAsync(containerId);
            finalStatus.Should().Be(Core.ValueObjects.ContainerStatus.Running);

            // Verify through Docker client directly
            var containerInfo = await _dockerClient.Containers.InspectContainerAsync(containerId.Value);
            containerInfo.State.Running.Should().BeTrue();
            containerInfo.State.Status.Should().Be("running");
        }
        catch (Exception)
        {
            await CleanupContainerByNameAsync(containerName);
            throw;
        }
    }

    [Fact]
    public async Task StopContainerAsync_WithValidContainerId_ShouldStopRealContainer()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:alpine");
        var containerName = $"test-stop-{Guid.NewGuid():N}";
        var timeoutSeconds = 10;

        try
        {
            // Ensure image exists
            await EnsureImageExistsAsync(imageTag);

            // Create and start container
            var createResult = await _dockerService.CreateContainerAsync(imageTag, containerName);
            var containerId = ContainerId.From(createResult.Value);
            _createdContainerIds.Add(createResult.Value);

            var startResult = await _dockerService.StartContainerAsync(containerId);
            startResult.Should().BeTrue();

            // Verify container is running
            var runningStatus = await _dockerService.GetContainerStatusAsync(containerId);
            runningStatus.Should().Be(Core.ValueObjects.ContainerStatus.Running);

            // Act
            var result = await _dockerService.StopContainerAsync(containerId, timeoutSeconds);

            // Assert
            result.Should().BeTrue();

            // Verify container is now stopped
            var stoppedStatus = await _dockerService.GetContainerStatusAsync(containerId);
            stoppedStatus.Should().Be(Core.ValueObjects.ContainerStatus.Stopped);

            // Verify through Docker client directly
            var containerInfo = await _dockerClient.Containers.InspectContainerAsync(containerId.Value);
            containerInfo.State.Running.Should().BeFalse();
            containerInfo.State.Status.Should().Be("exited");
        }
        catch (Exception)
        {
            await CleanupContainerByNameAsync(containerName);
            throw;
        }
    }

    [Fact]
    public async Task ListContainersAsync_ShouldReturnRealContainerList()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:alpine");
        var containerName1 = $"test-list-1-{Guid.NewGuid():N}";
        var containerName2 = $"test-list-2-{Guid.NewGuid():N}";

        try
        {
            // Ensure image exists
            await EnsureImageExistsAsync(imageTag);

            // Create two containers
            var createResult1 = await _dockerService.CreateContainerAsync(imageTag, containerName1);
            var createResult2 = await _dockerService.CreateContainerAsync(imageTag, containerName2);

            _createdContainerIds.Add(createResult1.Value);
            _createdContainerIds.Add(createResult2.Value);

            // Start one container
            var containerId1 = ContainerId.From(createResult1.Value);
            await _dockerService.StartContainerAsync(containerId1);

            // Act
            var result = await _dockerService.ListContainersAsync(includeAll: true);

            // Assert
            result.Should().NotBeEmpty();

            // Find our test containers
            var testContainers = result.Where(c =>
                c.Name == containerName1 || c.Name == containerName2).ToList();

            testContainers.Should().HaveCount(2);

            // Verify one is running, one is stopped
            var runningContainer = testContainers.FirstOrDefault(c =>
                c.Status == Core.ValueObjects.ContainerStatus.Running);
            var stoppedContainer = testContainers.FirstOrDefault(c =>
                c.Status == Core.ValueObjects.ContainerStatus.Stopped);

            runningContainer.Should().NotBeNull();
            stoppedContainer.Should().NotBeNull();
        }
        catch (Exception)
        {
            await CleanupContainerByNameAsync(containerName1);
            await CleanupContainerByNameAsync(containerName2);
            throw;
        }
    }

    [Fact]
    public async Task CompleteContainerLifecycle_ShouldWorkEndToEnd()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:alpine");
        var containerName = $"test-lifecycle-{Guid.NewGuid():N}";
        var envVars = new List<EnvironmentVariable>
        {
            EnvironmentVariable.Create("NGINX_PORT", "80"),
            EnvironmentVariable.Create("ENVIRONMENT", "test")
        };

        try
        {
            // Ensure image exists
            await EnsureImageExistsAsync(imageTag);

            // Step 1: Create container with environment variables
            var createResult = await _dockerService.CreateContainerAsync(imageTag, containerName, envVars);
            createResult.Should().NotBeNull();
            createResult.Value.Should().NotBeNullOrEmpty();

            var containerId = ContainerId.From(createResult.Value);
            _createdContainerIds.Add(createResult.Value);

            // Verify container exists and has correct configuration
            var containerInfo = await _dockerClient.Containers.InspectContainerAsync(containerId.Value);
            containerInfo.Config.Env.Should().Contain("NGINX_PORT=80");
            containerInfo.Config.Env.Should().Contain("ENVIRONMENT=test");

            // Step 2: Start container
            var startResult = await _dockerService.StartContainerAsync(containerId);
            startResult.Should().BeTrue();

            // Verify container is running
            var runningStatus = await _dockerService.GetContainerStatusAsync(containerId);
            runningStatus.Should().Be(Core.ValueObjects.ContainerStatus.Running);

            // Step 3: Stop container
            var stopResult = await _dockerService.StopContainerAsync(containerId, 10);
            stopResult.Should().BeTrue();

            // Verify container is stopped
            var stoppedStatus = await _dockerService.GetContainerStatusAsync(containerId);
            stoppedStatus.Should().Be(Core.ValueObjects.ContainerStatus.Stopped);

            // Step 4: Remove container
            await _dockerService.RemoveContainerAsync(containerId);

            // Verify container no longer exists
            var containerExists = await ContainerExistsAsync(containerId.Value);
            containerExists.Should().BeFalse();

            // Remove from tracking since it's already cleaned up
            _createdContainerIds.Remove(containerId.Value);
        }
        catch (Exception)
        {
            await CleanupContainerByNameAsync(containerName);
            throw;
        }
    }

    /// <summary>
    /// Initialize test environment
    /// </summary>
    public async Task InitializeAsync()
    {
        // Verify Docker is available
        try
        {
            await _dockerClient.System.PingAsync();
        }
        catch (Exception ex)
        {
            // Skip integration tests if Docker is not available
            throw new InvalidOperationException($"SKIPPED: Docker is not available for integration tests: {ex.Message}");
        }
    }

    /// <summary>
    /// Cleanup test environment
    /// </summary>
    public async Task DisposeAsync()
    {
        // Cleanup all created containers
        foreach (var containerId in _createdContainerIds)
        {
            try
            {
                await CleanupContainerAsync(containerId);
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        _dockerService?.Dispose();
        _dockerClient?.Dispose();
    }

    /// <summary>
    /// Ensure Docker image exists locally (pull if needed)
    /// </summary>
    private async Task EnsureImageExistsAsync(ImageTag imageTag)
    {
        try
        {
            // Check if image exists
            var images = await _dockerClient.Images.ListImagesAsync(new ImagesListParameters
            {
                Filters = new Dictionary<string, IDictionary<string, bool>>
                {
                    ["reference"] = new Dictionary<string, bool> { [imageTag.Value] = true }
                }
            });

            if (!images.Any())
            {
                // Pull image if not exists
                await _dockerClient.Images.CreateImageAsync(
                    new ImagesCreateParameters
                    {
                        FromImage = imageTag.Repository,
                        Tag = imageTag.Tag
                    },
                    null,
                    new Progress<JSONMessage>());
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to ensure image {imageTag.Value} exists", ex);
        }
    }

    /// <summary>
    /// Check if container exists in Docker
    /// </summary>
    private async Task<bool> ContainerExistsAsync(string containerId)
    {
        try
        {
            await _dockerClient.Containers.InspectContainerAsync(containerId);
            return true;
        }
        catch (DockerContainerNotFoundException)
        {
            return false;
        }
    }

    /// <summary>
    /// Cleanup container by ID
    /// </summary>
    private async Task CleanupContainerAsync(string containerId)
    {
        try
        {
            // Stop container if running
            await _dockerClient.Containers.StopContainerAsync(containerId, new ContainerStopParameters());
        }
        catch
        {
            // Ignore stop errors
        }

        try
        {
            // Remove container
            await _dockerClient.Containers.RemoveContainerAsync(containerId, new ContainerRemoveParameters
            {
                Force = true
            });
        }
        catch
        {
            // Ignore remove errors
        }
    }

    /// <summary>
    /// Cleanup container by name
    /// </summary>
    private async Task CleanupContainerByNameAsync(string containerName)
    {
        try
        {
            var containers = await _dockerClient.Containers.ListContainersAsync(new ContainersListParameters
            {
                All = true,
                Filters = new Dictionary<string, IDictionary<string, bool>>
                {
                    ["name"] = new Dictionary<string, bool> { [containerName] = true }
                }
            });

            foreach (var container in containers)
            {
                await CleanupContainerAsync(container.ID);
            }
        }
        catch
        {
            // Ignore cleanup errors
        }
    }
}
