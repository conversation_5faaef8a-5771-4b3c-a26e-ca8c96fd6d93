using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using AutoInstaller.UI.Views;
using AutoInstaller.UI.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using AvaloniaApplication = Avalonia.Application;

namespace AutoInstaller.UI;

/// <summary>
/// Main application class
/// </summary>
public partial class App : AvaloniaApplication
{
    private IHost? _host;

    /// <summary>
    /// Initialize the application
    /// </summary>
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    /// <summary>
    /// Called when the application framework initialization is completed
    /// </summary>
    public override void OnFrameworkInitializationCompleted()
    {
        try
        {
            // Build the host with dependency injection
            _host = CreateHostBuilder().Build();
            
            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                // Create main window with dependency injection
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                desktop.MainWindow = mainWindow;
                
                Log.Information("Application initialized successfully");
            }

            base.OnFrameworkInitializationCompleted();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Failed to initialize application");
            throw;
        }
    }

    /// <summary>
    /// Create the host builder with all services configured
    /// </summary>
    /// <returns>Configured host builder</returns>
    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(AppDomain.CurrentDomain.BaseDirectory);
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
                config.AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                // Register application services here
                ConfigureServices(services, context.Configuration);
            });
    }

    /// <summary>
    /// Configure dependency injection services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Application configuration</param>
    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Register views and view models
        services.AddTransient<MainWindow>();
        services.AddTransient<MainWindowViewModel>();
        
        // Additional services will be registered here by other agents
        Log.Information("Services configured successfully");
    }

    /// <summary>
    /// Clean up resources when application exits
    /// </summary>
    public void OnExit()
    {
        _host?.Dispose();
        Log.Information("Application exited");
    }
}
