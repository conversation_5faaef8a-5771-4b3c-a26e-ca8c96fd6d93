<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>AutoInstaller.Application</RootNamespace>
    <AssemblyName>AutoInstaller.Application</AssemblyName>
    <Description>Application layer implementing CQRS patterns, commands, queries, and handlers for the Auto-Installer Desktop application.</Description>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoInstaller.Core\AutoInstaller.Core.csproj" />
    <ProjectReference Include="..\AutoInstaller.Shared\AutoInstaller.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
  </ItemGroup>

</Project>
