using MediatR;
using Microsoft.Extensions.Logging;
using AutoInstaller.Application.Commands;
using AutoInstaller.Core.Interfaces.Repositories;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Application.Handlers;

/// <summary>
/// Handler for starting containers
/// </summary>
public sealed class StartContainerHandler : IRequestHandler<StartContainerCommand, ContainerOperationResult>
{
    private readonly IContainerRepository _containerRepository;
    private readonly ILogger<StartContainerHandler> _logger;
    private readonly IMediator _mediator;

    public StartContainerHandler(
        IContainerRepository containerRepository,
        ILogger<StartContainerHandler> logger,
        IMediator mediator)
    {
        _containerRepository = containerRepository;
        _logger = logger;
        _mediator = mediator;
    }

    public async Task<ContainerOperationResult> Handle(StartContainerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting container {ContainerIdOrName}", request.ContainerIdOrName);

            // Try to find container by ID first, then by name
            var container = await FindContainerAsync(request.ContainerIdOrName, cancellationToken);
            
            if (container == null)
            {
                return CreateFailureResult(request.ContainerIdOrName, "Start", "Container not found");
            }

            // Start the container
            container.Start();
            await _containerRepository.UpdateAsync(container, cancellationToken);

            // Publish domain events
            foreach (var domainEvent in container.DomainEvents)
            {
                await _mediator.Publish(domainEvent, cancellationToken);
            }
            container.ClearDomainEvents();

            _logger.LogInformation("Container {ContainerName} started successfully", container.Name);

            return new ContainerOperationResult
            {
                ContainerId = container.Id.Value,
                Name = container.Name,
                Operation = "Start",
                Status = container.Status.ToString(),
                Success = true,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start container {ContainerIdOrName}: {Error}", 
                request.ContainerIdOrName, ex.Message);
            
            return CreateFailureResult(request.ContainerIdOrName, "Start", ex.Message);
        }
    }

    private async Task<Core.Entities.Container?> FindContainerAsync(string idOrName, CancellationToken cancellationToken)
    {
        // Try by ID first
        if (ContainerId.From(idOrName) is var containerId)
        {
            var container = await _containerRepository.GetByIdAsync(containerId, cancellationToken);
            if (container != null) return container;
        }

        // Try by name
        return await _containerRepository.GetByNameAsync(idOrName, cancellationToken);
    }

    private static ContainerOperationResult CreateFailureResult(string containerIdOrName, string operation, string errorMessage)
    {
        return new ContainerOperationResult
        {
            ContainerId = containerIdOrName,
            Name = containerIdOrName,
            Operation = operation,
            Status = "Failed",
            Success = false,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Handler for stopping containers
/// </summary>
public sealed class StopContainerHandler : IRequestHandler<StopContainerCommand, ContainerOperationResult>
{
    private readonly IContainerRepository _containerRepository;
    private readonly ILogger<StopContainerHandler> _logger;
    private readonly IMediator _mediator;

    public StopContainerHandler(
        IContainerRepository containerRepository,
        ILogger<StopContainerHandler> logger,
        IMediator mediator)
    {
        _containerRepository = containerRepository;
        _logger = logger;
        _mediator = mediator;
    }

    public async Task<ContainerOperationResult> Handle(StopContainerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Stopping container {ContainerIdOrName} with timeout {TimeoutSeconds}s", 
                request.ContainerIdOrName, request.TimeoutSeconds);

            var container = await FindContainerAsync(request.ContainerIdOrName, cancellationToken);
            
            if (container == null)
            {
                return CreateFailureResult(request.ContainerIdOrName, "Stop", "Container not found");
            }

            container.Stop();
            await _containerRepository.UpdateAsync(container, cancellationToken);

            // Publish domain events
            foreach (var domainEvent in container.DomainEvents)
            {
                await _mediator.Publish(domainEvent, cancellationToken);
            }
            container.ClearDomainEvents();

            _logger.LogInformation("Container {ContainerName} stopped successfully", container.Name);

            return new ContainerOperationResult
            {
                ContainerId = container.Id.Value,
                Name = container.Name,
                Operation = "Stop",
                Status = container.Status.ToString(),
                Success = true,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop container {ContainerIdOrName}: {Error}", 
                request.ContainerIdOrName, ex.Message);
            
            return CreateFailureResult(request.ContainerIdOrName, "Stop", ex.Message);
        }
    }

    private async Task<Core.Entities.Container?> FindContainerAsync(string idOrName, CancellationToken cancellationToken)
    {
        try
        {
            var containerId = ContainerId.From(idOrName);
            var container = await _containerRepository.GetByIdAsync(containerId, cancellationToken);
            if (container != null) return container;
        }
        catch
        {
            // Invalid ID format, try by name
        }

        return await _containerRepository.GetByNameAsync(idOrName, cancellationToken);
    }

    private static ContainerOperationResult CreateFailureResult(string containerIdOrName, string operation, string errorMessage)
    {
        return new ContainerOperationResult
        {
            ContainerId = containerIdOrName,
            Name = containerIdOrName,
            Operation = operation,
            Status = "Failed",
            Success = false,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Handler for removing containers
/// </summary>
public sealed class RemoveContainerHandler : IRequestHandler<RemoveContainerCommand, ContainerOperationResult>
{
    private readonly IContainerRepository _containerRepository;
    private readonly ILogger<RemoveContainerHandler> _logger;
    private readonly IMediator _mediator;

    public RemoveContainerHandler(
        IContainerRepository containerRepository,
        ILogger<RemoveContainerHandler> logger,
        IMediator mediator)
    {
        _containerRepository = containerRepository;
        _logger = logger;
        _mediator = mediator;
    }

    public async Task<ContainerOperationResult> Handle(RemoveContainerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Removing container {ContainerIdOrName} (Force: {Force}, RemoveVolumes: {RemoveVolumes})", 
                request.ContainerIdOrName, request.Force, request.RemoveVolumes);

            var container = await FindContainerAsync(request.ContainerIdOrName, cancellationToken);
            
            if (container == null)
            {
                return CreateFailureResult(request.ContainerIdOrName, "Remove", "Container not found");
            }

            // Force stop if needed
            if (request.Force && container.Status == ContainerStatus.Running)
            {
                container.Stop();
            }

            container.Remove();
            await _containerRepository.UpdateAsync(container, cancellationToken);

            // Publish domain events
            foreach (var domainEvent in container.DomainEvents)
            {
                await _mediator.Publish(domainEvent, cancellationToken);
            }
            container.ClearDomainEvents();

            // Remove from repository
            await _containerRepository.RemoveAsync(container, cancellationToken);

            _logger.LogInformation("Container {ContainerName} removed successfully", container.Name);

            return new ContainerOperationResult
            {
                ContainerId = container.Id.Value,
                Name = container.Name,
                Operation = "Remove",
                Status = container.Status.ToString(),
                Success = true,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove container {ContainerIdOrName}: {Error}", 
                request.ContainerIdOrName, ex.Message);
            
            return CreateFailureResult(request.ContainerIdOrName, "Remove", ex.Message);
        }
    }

    private async Task<Core.Entities.Container?> FindContainerAsync(string idOrName, CancellationToken cancellationToken)
    {
        try
        {
            var containerId = ContainerId.From(idOrName);
            var container = await _containerRepository.GetByIdAsync(containerId, cancellationToken);
            if (container != null) return container;
        }
        catch
        {
            // Invalid ID format, try by name
        }

        return await _containerRepository.GetByNameAsync(idOrName, cancellationToken);
    }

    private static ContainerOperationResult CreateFailureResult(string containerIdOrName, string operation, string errorMessage)
    {
        return new ContainerOperationResult
        {
            ContainerId = containerIdOrName,
            Name = containerIdOrName,
            Operation = operation,
            Status = "Failed",
            Success = false,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.UtcNow
        };
    }
}
