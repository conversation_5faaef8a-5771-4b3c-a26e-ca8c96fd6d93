using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Models;

namespace AutoInstaller.Core.Interfaces;

/// <summary>
/// Interface for Docker operations using Docker.DotNet
/// </summary>
public interface IDockerService : IDisposable
{
    /// <summary>
    /// Creates a new Docker container from the specified image
    /// </summary>
    /// <param name="imageTag">The image tag to create the container from</param>
    /// <param name="containerName">The name for the new container</param>
    /// <param name="environmentVariables">Optional environment variables to set</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The ID of the created container</returns>
    Task<ContainerId> CreateContainerAsync(
        ImageTag imageTag, 
        string containerName, 
        IEnumerable<EnvironmentVariable>? environmentVariables = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Starts a Docker container
    /// </summary>
    /// <param name="containerId">The ID of the container to start</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the container was started successfully</returns>
    Task<bool> StartContainerAsync(ContainerId containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops a Docker container
    /// </summary>
    /// <param name="containerId">The ID of the container to stop</param>
    /// <param name="timeoutSeconds">Timeout in seconds before forcefully killing the container</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the container was stopped successfully</returns>
    Task<bool> StopContainerAsync(
        ContainerId containerId, 
        int timeoutSeconds = 10, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a Docker container
    /// </summary>
    /// <param name="containerId">The ID of the container to remove</param>
    /// <param name="force">Whether to force removal of a running container</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveContainerAsync(
        ContainerId containerId, 
        bool force = false, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current status of a Docker container
    /// </summary>
    /// <param name="containerId">The ID of the container</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The current status of the container</returns>
    Task<ContainerStatus> GetContainerStatusAsync(
        ContainerId containerId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Lists all Docker containers
    /// </summary>
    /// <param name="includeAll">Whether to include stopped containers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A collection of container information</returns>
    Task<IEnumerable<ContainerInfo>> ListContainersAsync(
        bool includeAll = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Pulls a Docker image from a registry
    /// </summary>
    /// <param name="imageTag">The image tag to pull</param>
    /// <param name="progress">Optional progress reporter for pull status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PullImageAsync(
        ImageTag imageTag,
        IProgress<string>? progress = null,
        CancellationToken cancellationToken = default);
}
