namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Enumeration representing Docker network drivers
/// </summary>
public enum NetworkDriver
{
    /// <summary>
    /// Bridge network driver (default)
    /// </summary>
    Bridge,

    /// <summary>
    /// Host network driver
    /// </summary>
    Host,

    /// <summary>
    /// Overlay network driver (for swarm mode)
    /// </summary>
    Overlay,

    /// <summary>
    /// MACVLAN network driver
    /// </summary>
    Macvlan,

    /// <summary>
    /// IPVLAN network driver
    /// </summary>
    Ipvlan,

    /// <summary>
    /// None network driver (no networking)
    /// </summary>
    None,

    /// <summary>
    /// Custom network driver
    /// </summary>
    Custom
}

/// <summary>
/// Extension methods for NetworkDriver
/// </summary>
public static class NetworkDriverExtensions
{
    /// <summary>
    /// Get display name for network driver
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <returns>Human-readable driver name</returns>
    public static string GetDisplayName(this NetworkDriver driver)
    {
        return driver switch
        {
            NetworkDriver.Bridge => "Bridge",
            NetworkDriver.Host => "Host",
            NetworkDriver.Overlay => "Overlay",
            NetworkDriver.Macvlan => "MACVLAN",
            NetworkDriver.Ipvlan => "IPVLAN",
            NetworkDriver.None => "None",
            NetworkDriver.Custom => "Custom",
            _ => driver.ToString()
        };
    }

    /// <summary>
    /// Get description for network driver
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <returns>Driver description</returns>
    public static string GetDescription(this NetworkDriver driver)
    {
        return driver switch
        {
            NetworkDriver.Bridge => "Default network driver. Creates a private internal network on the host.",
            NetworkDriver.Host => "Removes network isolation between container and host. Container uses host's network directly.",
            NetworkDriver.Overlay => "Creates a distributed network among multiple Docker daemon hosts. Used in swarm mode.",
            NetworkDriver.Macvlan => "Assigns a MAC address to container, making it appear as a physical device on the network.",
            NetworkDriver.Ipvlan => "Similar to MACVLAN but uses the same MAC address as the host interface.",
            NetworkDriver.None => "Disables all networking for the container.",
            NetworkDriver.Custom => "Third-party or custom network driver.",
            _ => "Unknown network driver"
        };
    }

    /// <summary>
    /// Check if driver supports custom subnets
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <returns>True if driver supports custom subnets</returns>
    public static bool SupportsCustomSubnet(this NetworkDriver driver)
    {
        return driver is NetworkDriver.Bridge or NetworkDriver.Overlay or NetworkDriver.Macvlan or NetworkDriver.Ipvlan;
    }

    /// <summary>
    /// Check if driver supports port mapping
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <returns>True if driver supports port mapping</returns>
    public static bool SupportsPortMapping(this NetworkDriver driver)
    {
        return driver is NetworkDriver.Bridge or NetworkDriver.Host;
    }

    /// <summary>
    /// Check if driver is suitable for production
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <returns>True if driver is production-ready</returns>
    public static bool IsProductionReady(this NetworkDriver driver)
    {
        return driver is NetworkDriver.Bridge or NetworkDriver.Overlay or NetworkDriver.Macvlan or NetworkDriver.Ipvlan;
    }

    /// <summary>
    /// Parse driver from Docker API string
    /// </summary>
    /// <param name="driverString">Docker driver string</param>
    /// <returns>Network driver enum</returns>
    public static NetworkDriver FromDockerString(string driverString)
    {
        if (string.IsNullOrWhiteSpace(driverString))
            return NetworkDriver.Bridge;

        return driverString.ToLowerInvariant() switch
        {
            "bridge" => NetworkDriver.Bridge,
            "host" => NetworkDriver.Host,
            "overlay" => NetworkDriver.Overlay,
            "macvlan" => NetworkDriver.Macvlan,
            "ipvlan" => NetworkDriver.Ipvlan,
            "null" or "none" => NetworkDriver.None,
            _ => NetworkDriver.Custom
        };
    }

    /// <summary>
    /// Convert to Docker API string
    /// </summary>
    /// <param name="driver">Network driver</param>
    /// <returns>Docker API driver string</returns>
    public static string ToDockerString(this NetworkDriver driver)
    {
        return driver switch
        {
            NetworkDriver.Bridge => "bridge",
            NetworkDriver.Host => "host",
            NetworkDriver.Overlay => "overlay",
            NetworkDriver.Macvlan => "macvlan",
            NetworkDriver.Ipvlan => "ipvlan",
            NetworkDriver.None => "null",
            NetworkDriver.Custom => "custom",
            _ => "bridge"
        };
    }
}

/// <summary>
/// Enumeration representing Docker network scope
/// </summary>
public enum NetworkScope
{
    /// <summary>
    /// Local scope (single host)
    /// </summary>
    Local,

    /// <summary>
    /// Global scope (multiple hosts)
    /// </summary>
    Global,

    /// <summary>
    /// Swarm scope (Docker swarm)
    /// </summary>
    Swarm
}

/// <summary>
/// Extension methods for NetworkScope
/// </summary>
public static class NetworkScopeExtensions
{
    /// <summary>
    /// Get display name for network scope
    /// </summary>
    /// <param name="scope">Network scope</param>
    /// <returns>Human-readable scope name</returns>
    public static string GetDisplayName(this NetworkScope scope)
    {
        return scope switch
        {
            NetworkScope.Local => "Local",
            NetworkScope.Global => "Global",
            NetworkScope.Swarm => "Swarm",
            _ => scope.ToString()
        };
    }

    /// <summary>
    /// Parse scope from Docker API string
    /// </summary>
    /// <param name="scopeString">Docker scope string</param>
    /// <returns>Network scope enum</returns>
    public static NetworkScope FromDockerString(string scopeString)
    {
        if (string.IsNullOrWhiteSpace(scopeString))
            return NetworkScope.Local;

        return scopeString.ToLowerInvariant() switch
        {
            "local" => NetworkScope.Local,
            "global" => NetworkScope.Global,
            "swarm" => NetworkScope.Swarm,
            _ => NetworkScope.Local
        };
    }

    /// <summary>
    /// Convert to Docker API string
    /// </summary>
    /// <param name="scope">Network scope</param>
    /// <returns>Docker API scope string</returns>
    public static string ToDockerString(this NetworkScope scope)
    {
        return scope switch
        {
            NetworkScope.Local => "local",
            NetworkScope.Global => "global",
            NetworkScope.Swarm => "swarm",
            _ => "local"
        };
    }
}
