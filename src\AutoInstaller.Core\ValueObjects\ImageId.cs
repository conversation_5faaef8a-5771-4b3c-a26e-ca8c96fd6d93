namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value object representing a Docker image identifier
/// </summary>
public sealed record ImageId
{
    /// <summary>
    /// Image ID value
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Initialize a new image ID
    /// </summary>
    /// <param name="value">Image ID value</param>
    private ImageId(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Create a new image ID from string value
    /// </summary>
    /// <param name="value">Image ID string</param>
    /// <returns>Image ID instance</returns>
    /// <exception cref="ArgumentException">Thrown when value is invalid</exception>
    public static ImageId From(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Image ID cannot be null or empty", nameof(value));

        // Remove sha256: prefix if present
        if (value.StartsWith("sha256:", StringComparison.OrdinalIgnoreCase))
        {
            value = value[7..];
        }

        if (value.Length < 12)
            throw new ArgumentException("Image ID must be at least 12 characters long", nameof(value));

        if (value.Length > 64)
            throw new ArgumentException("Image ID cannot be longer than 64 characters", nameof(value));

        if (!IsValidHexString(value))
            throw new ArgumentException("Image ID must contain only hexadecimal characters", nameof(value));

        return new ImageId(value.ToLowerInvariant());
    }

    /// <summary>
    /// Generate a new unique image ID
    /// </summary>
    /// <returns>New image ID instance</returns>
    public static ImageId New()
    {
        // Generate a 64-character hexadecimal string similar to Docker image IDs
        var guid1 = Guid.NewGuid().ToString("N");
        var guid2 = Guid.NewGuid().ToString("N");
        var imageIdValue = (guid1 + guid2)[..64];
        
        return new ImageId(imageIdValue);
    }

    /// <summary>
    /// Get short version of image ID (first 12 characters)
    /// </summary>
    /// <returns>Short image ID</returns>
    public string GetShortId()
    {
        return Value[..12];
    }

    /// <summary>
    /// Get full SHA256 format with prefix
    /// </summary>
    /// <returns>SHA256 prefixed image ID</returns>
    public string GetSha256Format()
    {
        return $"sha256:{Value}";
    }

    /// <summary>
    /// Check if this is a short image ID
    /// </summary>
    /// <returns>True if ID is 12 characters or less</returns>
    public bool IsShortId()
    {
        return Value.Length <= 12;
    }

    /// <summary>
    /// Check if this is a full image ID
    /// </summary>
    /// <returns>True if ID is longer than 12 characters</returns>
    public bool IsFullId()
    {
        return Value.Length > 12;
    }

    /// <summary>
    /// Validate if string contains only hexadecimal characters
    /// </summary>
    /// <param name="value">String to validate</param>
    /// <returns>True if string is valid hexadecimal</returns>
    private static bool IsValidHexString(string value)
    {
        return value.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    /// <summary>
    /// Implicit conversion from string to ImageId
    /// </summary>
    /// <param name="value">String value</param>
    public static implicit operator ImageId(string value) => From(value);

    /// <summary>
    /// Implicit conversion from ImageId to string
    /// </summary>
    /// <param name="imageId">Image ID</param>
    public static implicit operator string(ImageId imageId) => imageId.Value;

    /// <summary>
    /// String representation of image ID
    /// </summary>
    /// <returns>Image ID value</returns>
    public override string ToString() => Value;
}
