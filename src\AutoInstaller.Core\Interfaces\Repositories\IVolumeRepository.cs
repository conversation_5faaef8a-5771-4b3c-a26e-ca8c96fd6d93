using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Interfaces.Repositories;

/// <summary>
/// Repository interface for Volume entity
/// </summary>
public interface IVolumeRepository
{
    /// <summary>
    /// Get volume by ID
    /// </summary>
    /// <param name="id">Volume ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Volume if found, null otherwise</returns>
    Task<Volume?> GetByIdAsync(VolumeId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get volume by name
    /// </summary>
    /// <param name="name">Volume name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Volume if found, null otherwise</returns>
    Task<Volume?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all volumes
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all volumes</returns>
    Task<IReadOnlyList<Volume>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get volumes by driver
    /// </summary>
    /// <param name="driver">Volume driver</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of volumes with specified driver</returns>
    Task<IReadOnlyList<Volume>> GetByDriverAsync(string driver, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get volumes in use (mounted to containers)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of volumes currently in use</returns>
    Task<IReadOnlyList<Volume>> GetInUseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get unused volumes (not mounted to any container)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of unused volumes</returns>
    Task<IReadOnlyList<Volume>> GetUnusedAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get volumes by container
    /// </summary>
    /// <param name="containerId">Container ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of volumes mounted to the container</returns>
    Task<IReadOnlyList<Volume>> GetByContainerAsync(ContainerId containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get volumes larger than specified size
    /// </summary>
    /// <param name="sizeInBytes">Minimum size in bytes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of large volumes</returns>
    Task<IReadOnlyList<Volume>> GetLargerThanAsync(long sizeInBytes, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get read-only volumes
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of read-only volumes</returns>
    Task<IReadOnlyList<Volume>> GetReadOnlyAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if volume exists by ID
    /// </summary>
    /// <param name="id">Volume ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if volume exists</returns>
    Task<bool> ExistsAsync(VolumeId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if volume name is available
    /// </summary>
    /// <param name="name">Volume name</param>
    /// <param name="excludeId">Volume ID to exclude from check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if name is available</returns>
    Task<bool> IsNameAvailableAsync(string name, VolumeId? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add new volume
    /// </summary>
    /// <param name="volume">Volume to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task AddAsync(Volume volume, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing volume
    /// </summary>
    /// <param name="volume">Volume to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateAsync(Volume volume, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove volume
    /// </summary>
    /// <param name="volume">Volume to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveAsync(Volume volume, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove volume by ID
    /// </summary>
    /// <param name="id">Volume ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveByIdAsync(VolumeId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get volumes with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of volumes</returns>
    Task<(IReadOnlyList<Volume> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search volumes by name pattern
    /// </summary>
    /// <param name="namePattern">Name pattern to search</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching volumes</returns>
    Task<IReadOnlyList<Volume>> SearchByNameAsync(string namePattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get total size of all volumes
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total size in bytes (null if not available)</returns>
    Task<long?> GetTotalSizeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get volume count
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total number of volumes</returns>
    Task<int> GetCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get unused volume count
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of unused volumes</returns>
    Task<int> GetUnusedCountAsync(CancellationToken cancellationToken = default);
}
