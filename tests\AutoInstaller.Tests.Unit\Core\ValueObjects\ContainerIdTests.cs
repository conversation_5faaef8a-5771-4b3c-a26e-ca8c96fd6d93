using Xunit;
using FluentAssertions;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Tests.Unit.Core.ValueObjects;

/// <summary>
/// Unit tests for ContainerId value object
/// </summary>
public class ContainerIdTests
{
    [Fact]
    public void From_WithValidId_ShouldReturnContainerId()
    {
        // Arrange
        var validId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";

        // Act
        var result = ContainerId.From(validId);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(validId.ToLowerInvariant());
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void From_WithInvalidId_ShouldThrowArgumentException(string invalidId)
    {
        // Act & Assert
        var action = () => ContainerId.From(invalidId);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void From_WithNonHexCharacters_ShouldThrowArgumentException()
    {
        // Arrange
        var invalidId = "g1h2i3j4k5l6789012345678901234567890123456789012345678901234";

        // Act & Assert
        var action = () => ContainerId.From(invalidId);
        action.Should().Throw<ArgumentException>()
            .WithMessage("*hexadecimal characters*");
    }

    [Fact]
    public void New_ShouldGenerateValidContainerId()
    {
        // Act
        var containerId = ContainerId.New();

        // Assert
        containerId.Should().NotBeNull();
        containerId.Value.Should().HaveLength(64);
        containerId.Value.Should().MatchRegex("^[0-9a-f]{64}$");
    }

    [Fact]
    public void GetShortId_ShouldReturnFirst12Characters()
    {
        // Arrange
        var fullId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var containerId = ContainerId.From(fullId);

        // Act
        var shortId = containerId.GetShortId();

        // Assert
        shortId.Should().Be("a1b2c3d4e5f6");
        shortId.Should().HaveLength(12);
    }

    [Fact]
    public void Equals_WithSameValue_ShouldReturnTrue()
    {
        // Arrange
        var id = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var containerId1 = ContainerId.From(id);
        var containerId2 = ContainerId.From(id);

        // Act & Assert
        containerId1.Should().Be(containerId2);
        containerId1.GetHashCode().Should().Be(containerId2.GetHashCode());
    }

    [Fact]
    public void Equals_WithDifferentValue_ShouldReturnFalse()
    {
        // Arrange
        var id1 = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var id2 = "b1c2d3e4f5a6789012345678901234567890123456789012345678901234";
        var containerId1 = ContainerId.From(id1);
        var containerId2 = ContainerId.From(id2);

        // Act & Assert
        containerId1.Should().NotBe(containerId2);
    }

    [Fact]
    public void ToString_ShouldReturnValue()
    {
        // Arrange
        var id = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";
        var containerId = ContainerId.From(id);

        // Act
        var result = containerId.ToString();

        // Assert
        result.Should().Be(id);
    }

    [Fact]
    public void From_WithUppercaseId_ShouldConvertToLowercase()
    {
        // Arrange
        var uppercaseId = "A1B2C3D4E5F6789012345678901234567890123456789012345678901234";
        var expectedId = uppercaseId.ToLowerInvariant();

        // Act
        var containerId = ContainerId.From(uppercaseId);

        // Assert
        containerId.Value.Should().Be(expectedId);
    }
}
