using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Interfaces.Services;

/// <summary>
/// Domain service interface for Docker operations
/// </summary>
public interface IDockerService
{
    /// <summary>
    /// Check if Docker is available and running
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if Docker is available</returns>
    Task<bool> IsDockerAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get Docker version information
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Docker version info</returns>
    Task<DockerVersionInfo> GetVersionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get Docker system information
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Docker system info</returns>
    Task<DockerSystemInfo> GetSystemInfoAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate container configuration before creation
    /// </summary>
    /// <param name="name">Container name</param>
    /// <param name="imageTag">Image tag</param>
    /// <param name="portMappings">Port mappings</param>
    /// <param name="volumeMounts">Volume mounts</param>
    /// <param name="environmentVariables">Environment variables</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<ValidationResult> ValidateContainerConfigurationAsync(
        string name,
        ImageTag imageTag,
        IEnumerable<PortMapping> portMappings,
        IEnumerable<VolumeMount> volumeMounts,
        IEnumerable<EnvironmentVariable> environmentVariables,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if port is available on host
    /// </summary>
    /// <param name="port">Port number</param>
    /// <param name="protocol">Port protocol</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if port is available</returns>
    Task<bool> IsPortAvailableAsync(int port, PortProtocol protocol = PortProtocol.TCP, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if image exists locally
    /// </summary>
    /// <param name="imageTag">Image tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if image exists locally</returns>
    Task<bool> ImageExistsLocallyAsync(ImageTag imageTag, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if image exists in registry
    /// </summary>
    /// <param name="imageTag">Image tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if image exists in registry</returns>
    Task<bool> ImageExistsInRegistryAsync(ImageTag imageTag, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available networks for container connection
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available networks</returns>
    Task<IReadOnlyList<Network>> GetAvailableNetworksAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate network configuration
    /// </summary>
    /// <param name="name">Network name</param>
    /// <param name="driver">Network driver</param>
    /// <param name="subnet">Network subnet</param>
    /// <param name="gateway">Network gateway</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<ValidationResult> ValidateNetworkConfigurationAsync(
        string name,
        NetworkDriver driver,
        string? subnet = null,
        string? gateway = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate volume configuration
    /// </summary>
    /// <param name="name">Volume name</param>
    /// <param name="driver">Volume driver</param>
    /// <param name="mountpoint">Volume mountpoint</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<ValidationResult> ValidateVolumeConfigurationAsync(
        string name,
        string driver,
        string mountpoint,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if path exists on host
    /// </summary>
    /// <param name="path">Path to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if path exists</returns>
    Task<bool> PathExistsAsync(string path, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get disk usage information
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Disk usage info</returns>
    Task<DockerDiskUsage> GetDiskUsageAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Clean up unused Docker resources
    /// </summary>
    /// <param name="includeVolumes">Whether to include volumes in cleanup</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cleanup result</returns>
    Task<CleanupResult> CleanupUnusedResourcesAsync(bool includeVolumes = false, CancellationToken cancellationToken = default);
}

/// <summary>
/// Docker version information
/// </summary>
/// <param name="Version">Docker version</param>
/// <param name="ApiVersion">API version</param>
/// <param name="GitCommit">Git commit</param>
/// <param name="GoVersion">Go version</param>
/// <param name="Os">Operating system</param>
/// <param name="Arch">Architecture</param>
public sealed record DockerVersionInfo(
    string Version,
    string ApiVersion,
    string GitCommit,
    string GoVersion,
    string Os,
    string Arch
);

/// <summary>
/// Docker system information
/// </summary>
/// <param name="ContainersRunning">Number of running containers</param>
/// <param name="ContainersPaused">Number of paused containers</param>
/// <param name="ContainersStopped">Number of stopped containers</param>
/// <param name="Images">Number of images</param>
/// <param name="ServerVersion">Server version</param>
/// <param name="StorageDriver">Storage driver</param>
/// <param name="TotalMemory">Total memory in bytes</param>
/// <param name="CpuCount">Number of CPUs</param>
public sealed record DockerSystemInfo(
    int ContainersRunning,
    int ContainersPaused,
    int ContainersStopped,
    int Images,
    string ServerVersion,
    string StorageDriver,
    long TotalMemory,
    int CpuCount
);

/// <summary>
/// Validation result
/// </summary>
/// <param name="IsValid">Whether validation passed</param>
/// <param name="Errors">List of validation errors</param>
public sealed record ValidationResult(
    bool IsValid,
    IReadOnlyList<string> Errors
)
{
    /// <summary>
    /// Create successful validation result
    /// </summary>
    /// <returns>Valid result</returns>
    public static ValidationResult Success() => new(true, Array.Empty<string>());

    /// <summary>
    /// Create failed validation result
    /// </summary>
    /// <param name="errors">Validation errors</param>
    /// <returns>Invalid result</returns>
    public static ValidationResult Failure(params string[] errors) => new(false, errors);

    /// <summary>
    /// Create failed validation result
    /// </summary>
    /// <param name="errors">Validation errors</param>
    /// <returns>Invalid result</returns>
    public static ValidationResult Failure(IEnumerable<string> errors) => new(false, errors.ToArray());
}

/// <summary>
/// Docker disk usage information
/// </summary>
/// <param name="ImagesSize">Total size of images</param>
/// <param name="ContainersSize">Total size of containers</param>
/// <param name="VolumesSize">Total size of volumes</param>
/// <param name="BuildCacheSize">Total size of build cache</param>
public sealed record DockerDiskUsage(
    long ImagesSize,
    long ContainersSize,
    long VolumesSize,
    long BuildCacheSize
);

/// <summary>
/// Cleanup operation result
/// </summary>
/// <param name="ImagesDeleted">Number of images deleted</param>
/// <param name="ContainersDeleted">Number of containers deleted</param>
/// <param name="VolumesDeleted">Number of volumes deleted</param>
/// <param name="NetworksDeleted">Number of networks deleted</param>
/// <param name="SpaceReclaimed">Space reclaimed in bytes</param>
public sealed record CleanupResult(
    int ImagesDeleted,
    int ContainersDeleted,
    int VolumesDeleted,
    int NetworksDeleted,
    long SpaceReclaimed
);
