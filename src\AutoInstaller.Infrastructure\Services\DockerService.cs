using Docker.DotNet;
using Docker.DotNet.Models;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.Models;
using Microsoft.Extensions.Logging;
using System.Runtime.InteropServices;

namespace AutoInstaller.Infrastructure.Services;

/// <summary>
/// Docker service implementation using Docker.DotNet
/// </summary>
public class DockerService : IDockerService, IDisposable
{
    private readonly IDockerClient _dockerClient;
    private readonly ILogger<DockerService> _logger;
    private bool _disposed = false;

    public DockerService(IDockerClient dockerClient, ILogger<DockerService> logger)
    {
        _dockerClient = dockerClient ?? throw new ArgumentNullException(nameof(dockerClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public DockerService(ILogger<DockerService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Create Docker client based on platform
        var dockerUri = GetDockerUri();
        _dockerClient = new DockerClientConfiguration(dockerUri).CreateClient();
        
        _logger.LogInformation("Docker client initialized with URI: {DockerUri}", dockerUri);
    }

    public async Task<ContainerId> CreateContainerAsync(
        ImageTag imageTag, 
        string containerName, 
        IEnumerable<EnvironmentVariable>? environmentVariables = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating container {ContainerName} from image {ImageTag}", 
                containerName, imageTag.Value);

            var createParameters = new CreateContainerParameters
            {
                Image = imageTag.Value,
                Name = containerName,
                Env = environmentVariables?.Select(env => env.ToDockerString()).ToList() ?? new List<string>(),
                HostConfig = new HostConfig
                {
                    RestartPolicy = new RestartPolicy { Name = RestartPolicyKind.UnlessStopped }
                }
            };

            var response = await _dockerClient.Containers.CreateContainerAsync(
                createParameters, 
                cancellationToken);

            if (response.Warnings?.Any() == true)
            {
                foreach (var warning in response.Warnings)
                {
                    _logger.LogWarning("Docker container creation warning: {Warning}", warning);
                }
            }

            var containerId = ContainerId.From(response.ID);
            _logger.LogInformation("Container {ContainerName} created successfully with ID: {ContainerId}", 
                containerName, containerId.GetShortId());

            return containerId;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Failed to create container {ContainerName} from image {ImageTag}", 
                containerName, imageTag.Value);
            throw new InvalidOperationException($"Failed to create container: {ex.Message}", ex);
        }
    }

    public async Task<bool> StartContainerAsync(ContainerId containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting container {ContainerId}", containerId.GetShortId());

            var startParameters = new ContainerStartParameters();
            var result = await _dockerClient.Containers.StartContainerAsync(
                containerId.Value, 
                startParameters, 
                cancellationToken);

            _logger.LogInformation("Container {ContainerId} start result: {Result}", 
                containerId.GetShortId(), result);

            return result;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Failed to start container {ContainerId}", containerId.GetShortId());
            throw new InvalidOperationException($"Failed to start container: {ex.Message}", ex);
        }
    }

    public async Task<bool> StopContainerAsync(
        ContainerId containerId, 
        int timeoutSeconds = 10, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Stopping container {ContainerId} with timeout {TimeoutSeconds}s", 
                containerId.GetShortId(), timeoutSeconds);

            var stopParameters = new ContainerStopParameters
            {
                WaitBeforeKillSeconds = (uint)timeoutSeconds
            };

            var result = await _dockerClient.Containers.StopContainerAsync(
                containerId.Value, 
                stopParameters, 
                cancellationToken);

            _logger.LogInformation("Container {ContainerId} stop result: {Result}", 
                containerId.GetShortId(), result);

            return result;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Failed to stop container {ContainerId}", containerId.GetShortId());
            throw new InvalidOperationException($"Failed to stop container: {ex.Message}", ex);
        }
    }

    public async Task RemoveContainerAsync(
        ContainerId containerId, 
        bool force = false, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Removing container {ContainerId} (force: {Force})", 
                containerId.GetShortId(), force);

            var removeParameters = new ContainerRemoveParameters
            {
                Force = force,
                RemoveVolumes = true
            };

            await _dockerClient.Containers.RemoveContainerAsync(
                containerId.Value, 
                removeParameters, 
                cancellationToken);

            _logger.LogInformation("Container {ContainerId} removed successfully", containerId.GetShortId());
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Failed to remove container {ContainerId}", containerId.GetShortId());
            throw new InvalidOperationException($"Failed to remove container: {ex.Message}", ex);
        }
    }

    public async Task<Core.ValueObjects.ContainerStatus> GetContainerStatusAsync(
        ContainerId containerId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _dockerClient.Containers.InspectContainerAsync(
                containerId.Value, 
                cancellationToken);

            var status = MapDockerStateToContainerStatus(response.State);
            
            _logger.LogDebug("Container {ContainerId} status: {Status}", 
                containerId.GetShortId(), status);

            return status;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Failed to get status for container {ContainerId}", containerId.GetShortId());
            throw new InvalidOperationException($"Failed to get container status: {ex.Message}", ex);
        }
    }

    public async Task<IEnumerable<ContainerInfo>> ListContainersAsync(
        bool includeAll = false, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Listing containers (includeAll: {IncludeAll})", includeAll);

            var listParameters = new ContainersListParameters
            {
                All = includeAll
            };

            var containers = await _dockerClient.Containers.ListContainersAsync(
                listParameters, 
                cancellationToken);

            var containerInfos = containers.Select(container => new ContainerInfo
            {
                Id = ContainerId.From(container.ID),
                Name = container.Names?.FirstOrDefault()?.TrimStart('/') ?? "unknown",
                ImageTag = ImageTag.From(container.Image),
                Status = MapDockerStateStringToContainerStatus(container.State),
                StatusDescription = container.Status
            }).ToList();

            _logger.LogInformation("Found {ContainerCount} containers", containerInfos.Count);

            return containerInfos;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Failed to list containers");
            throw new InvalidOperationException($"Failed to list containers: {ex.Message}", ex);
        }
    }

    public async Task PullImageAsync(
        ImageTag imageTag,
        IProgress<string>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Pulling image {ImageTag}", imageTag.Value);

            var createParameters = new ImagesCreateParameters
            {
                FromImage = imageTag.Repository,
                Tag = imageTag.Tag
            };

            // Create a progress adapter to convert JSONMessage to string
            var progressAdapter = progress != null ? new Progress<JSONMessage>(msg =>
            {
                var status = msg.Status ?? "Unknown";
                var progressInfo = msg.ProgressMessage ?? "";
                progress.Report($"{status} {progressInfo}".Trim());
            }) : null;

            await _dockerClient.Images.CreateImageAsync(
                createParameters,
                null, // No authentication
                progressAdapter,
                cancellationToken);

            _logger.LogInformation("Image {ImageTag} pulled successfully", imageTag.Value);
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Failed to pull image {ImageTag}", imageTag.Value);
            throw new InvalidOperationException($"Failed to pull image: {ex.Message}", ex);
        }
    }

    private static Uri GetDockerUri()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return new Uri("npipe://./pipe/docker_engine");
        }
        else
        {
            return new Uri("unix:///var/run/docker.sock");
        }
    }

    private static Core.ValueObjects.ContainerStatus MapDockerStateToContainerStatus(ContainerState state)
    {
        if (state.Running) return Core.ValueObjects.ContainerStatus.Running;
        if (state.Paused) return Core.ValueObjects.ContainerStatus.Paused;
        if (state.Restarting) return Core.ValueObjects.ContainerStatus.Restarting;
        if (state.Dead) return Core.ValueObjects.ContainerStatus.Dead;
        if (state.ExitCode != 0) return Core.ValueObjects.ContainerStatus.Exited;

        return Core.ValueObjects.ContainerStatus.Stopped;
    }

    private static Core.ValueObjects.ContainerStatus MapDockerStateStringToContainerStatus(string state)
    {
        return state.ToLowerInvariant() switch
        {
            "running" => Core.ValueObjects.ContainerStatus.Running,
            "paused" => Core.ValueObjects.ContainerStatus.Paused,
            "restarting" => Core.ValueObjects.ContainerStatus.Restarting,
            "removing" => Core.ValueObjects.ContainerStatus.Removing,
            "dead" => Core.ValueObjects.ContainerStatus.Dead,
            "exited" => Core.ValueObjects.ContainerStatus.Exited,
            "created" => Core.ValueObjects.ContainerStatus.Created,
            _ => Core.ValueObjects.ContainerStatus.Unknown
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _dockerClient?.Dispose();
            _disposed = true;
        }
    }
}


