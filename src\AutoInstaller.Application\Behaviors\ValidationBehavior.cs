using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.Application.Behaviors;

/// <summary>
/// Validation behavior for MediatR pipeline
/// </summary>
/// <typeparam name="TRequest">Request type</typeparam>
/// <typeparam name="TResponse">Response type</typeparam>
public sealed class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;
    private readonly ILogger<ValidationBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Initialize the validation behavior
    /// </summary>
    /// <param name="validators">Validators for the request</param>
    /// <param name="logger">Logger</param>
    public ValidationBehavior(
        IEnumerable<IValidator<TRequest>> validators,
        ILogger<ValidationBehavior<TRequest, TResponse>> logger)
    {
        _validators = validators;
        _logger = logger;
    }

    /// <summary>
    /// Handle the request with validation
    /// </summary>
    /// <param name="request">Request</param>
    /// <param name="next">Next handler</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (!_validators.Any())
        {
            return await next();
        }

        var context = new ValidationContext<TRequest>(request);

        var validationResults = await Task.WhenAll(
            _validators.Select(v => v.ValidateAsync(context, cancellationToken)));

        var failures = validationResults
            .SelectMany(r => r.Errors)
            .Where(f => f != null)
            .ToList();

        if (failures.Any())
        {
            var requestName = typeof(TRequest).Name;
            var errors = failures.Select(f => f.ErrorMessage).ToArray();
            
            _logger.LogWarning("Validation failed for {RequestName}: {Errors}", 
                requestName, string.Join(", ", errors));

            throw new ValidationException(failures);
        }

        return await next();
    }
}

/// <summary>
/// Logging behavior for MediatR pipeline
/// </summary>
/// <typeparam name="TRequest">Request type</typeparam>
/// <typeparam name="TResponse">Response type</typeparam>
public sealed class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Initialize the logging behavior
    /// </summary>
    /// <param name="logger">Logger</param>
    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Handle the request with logging
    /// </summary>
    /// <param name="request">Request</param>
    /// <param name="next">Next handler</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();

        _logger.LogInformation("Handling {RequestName} with ID {RequestId}", requestName, requestId);

        try
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var response = await next();
            stopwatch.Stop();

            _logger.LogInformation("Completed {RequestName} with ID {RequestId} in {ElapsedMs}ms", 
                requestName, requestId, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle {RequestName} with ID {RequestId}: {Error}", 
                requestName, requestId, ex.Message);
            throw;
        }
    }
}

/// <summary>
/// Performance monitoring behavior for MediatR pipeline
/// </summary>
/// <typeparam name="TRequest">Request type</typeparam>
/// <typeparam name="TResponse">Response type</typeparam>
public sealed class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private const int SlowRequestThresholdMs = 5000; // 5 seconds

    /// <summary>
    /// Initialize the performance behavior
    /// </summary>
    /// <param name="logger">Logger</param>
    public PerformanceBehavior(ILogger<PerformanceBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Handle the request with performance monitoring
    /// </summary>
    /// <param name="request">Request</param>
    /// <param name="next">Next handler</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var response = await next();
        stopwatch.Stop();

        var elapsedMs = stopwatch.ElapsedMilliseconds;
        var requestName = typeof(TRequest).Name;

        if (elapsedMs > SlowRequestThresholdMs)
        {
            _logger.LogWarning("Slow request detected: {RequestName} took {ElapsedMs}ms to complete", 
                requestName, elapsedMs);
        }
        else
        {
            _logger.LogDebug("Request {RequestName} completed in {ElapsedMs}ms", 
                requestName, elapsedMs);
        }

        return response;
    }
}

/// <summary>
/// Caching behavior for MediatR pipeline (for queries only)
/// </summary>
/// <typeparam name="TRequest">Request type</typeparam>
/// <typeparam name="TResponse">Response type</typeparam>
public sealed class CachingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    private readonly ILogger<CachingBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Initialize the caching behavior
    /// </summary>
    /// <param name="logger">Logger</param>
    public CachingBehavior(ILogger<CachingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Handle the request with caching (placeholder implementation)
    /// </summary>
    /// <param name="request">Request</param>
    /// <param name="next">Next handler</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        // TODO: Implement caching logic for queries
        // For now, just pass through to next handler
        
        var requestName = typeof(TRequest).Name;
        
        // Only cache queries (requests that don't modify state)
        if (requestName.EndsWith("Query"))
        {
            _logger.LogDebug("Caching enabled for query {RequestName}", requestName);
            // TODO: Check cache first, return cached result if available
            // TODO: Cache result after execution
        }

        return await next();
    }
}
