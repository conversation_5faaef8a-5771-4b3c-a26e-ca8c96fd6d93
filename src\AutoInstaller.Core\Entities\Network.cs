using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Events;

namespace AutoInstaller.Core.Entities;

/// <summary>
/// Represents a Docker network entity in the domain
/// </summary>
public class Network
{
    private readonly List<DomainEvent> _domainEvents = new();
    private readonly List<ContainerId> _connectedContainers = new();

    /// <summary>
    /// Initialize a new network
    /// </summary>
    /// <param name="id">Network identifier</param>
    /// <param name="name">Network name</param>
    /// <param name="driver">Network driver</param>
    /// <param name="scope">Network scope</param>
    private Network(NetworkId id, string name, NetworkDriver driver, NetworkScope scope)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Name = !string.IsNullOrWhiteSpace(name) ? name : throw new ArgumentException("Network name cannot be empty", nameof(name));
        Driver = driver;
        Scope = scope;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        IsInternal = false;
        EnableIPv6 = false;
    }

    /// <summary>
    /// Network unique identifier
    /// </summary>
    public NetworkId Id { get; private set; }

    /// <summary>
    /// Network name
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// Network driver (bridge, host, overlay, etc.)
    /// </summary>
    public NetworkDriver Driver { get; private set; }

    /// <summary>
    /// Network scope (local, global, swarm)
    /// </summary>
    public NetworkScope Scope { get; private set; }

    /// <summary>
    /// Network creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; private set; }

    /// <summary>
    /// Network subnet (CIDR notation)
    /// </summary>
    public string? Subnet { get; private set; }

    /// <summary>
    /// Network gateway IP address
    /// </summary>
    public string? Gateway { get; private set; }

    /// <summary>
    /// Whether the network is internal (no external connectivity)
    /// </summary>
    public bool IsInternal { get; private set; }

    /// <summary>
    /// Whether IPv6 is enabled for this network
    /// </summary>
    public bool EnableIPv6 { get; private set; }

    /// <summary>
    /// Network description
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Containers connected to this network
    /// </summary>
    public IReadOnlyList<ContainerId> ConnectedContainers => _connectedContainers.AsReadOnly();

    /// <summary>
    /// Number of connected containers
    /// </summary>
    public int ContainerCount => _connectedContainers.Count;

    /// <summary>
    /// Whether this is a system network (created by Docker)
    /// </summary>
    public bool IsSystemNetwork => Name is "bridge" or "host" or "none";

    /// <summary>
    /// Domain events raised by this entity
    /// </summary>
    public IReadOnlyList<DomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Create a new network
    /// </summary>
    /// <param name="name">Network name</param>
    /// <param name="driver">Network driver</param>
    /// <param name="scope">Network scope</param>
    /// <param name="description">Optional description</param>
    /// <returns>New network instance</returns>
    public static Network Create(string name, NetworkDriver driver, NetworkScope scope, string? description = null)
    {
        var network = new Network(
            NetworkId.New(),
            name,
            driver,
            scope
        );

        if (!string.IsNullOrWhiteSpace(description))
        {
            network.Description = description;
        }

        network.AddDomainEvent(new NetworkCreatedEvent(network.Id, network.Name, network.Driver));
        return network;
    }

    /// <summary>
    /// Create a network with specific ID
    /// </summary>
    /// <param name="id">Network ID</param>
    /// <param name="name">Network name</param>
    /// <param name="driver">Network driver</param>
    /// <param name="scope">Network scope</param>
    /// <param name="description">Optional description</param>
    /// <returns>New network instance</returns>
    public static Network Create(NetworkId id, string name, NetworkDriver driver, NetworkScope scope, string? description = null)
    {
        var network = new Network(
            id,
            name,
            driver,
            scope
        );

        if (!string.IsNullOrWhiteSpace(description))
        {
            network.Description = description;
        }

        network.AddDomainEvent(new NetworkCreatedEvent(network.Id, network.Name, network.Driver));
        return network;
    }

    /// <summary>
    /// Configure network IP settings
    /// </summary>
    /// <param name="subnet">Network subnet in CIDR notation</param>
    /// <param name="gateway">Gateway IP address</param>
    public void ConfigureIPSettings(string? subnet = null, string? gateway = null)
    {
        bool hasChanges = false;

        if (Subnet != subnet)
        {
            Subnet = subnet;
            hasChanges = true;
        }

        if (Gateway != gateway)
        {
            Gateway = gateway;
            hasChanges = true;
        }

        if (hasChanges)
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new NetworkConfigurationUpdatedEvent(Id, Name));
        }
    }

    /// <summary>
    /// Set network as internal
    /// </summary>
    /// <param name="isInternal">Whether network should be internal</param>
    public void SetInternal(bool isInternal)
    {
        if (IsInternal != isInternal)
        {
            IsInternal = isInternal;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new NetworkConfigurationUpdatedEvent(Id, Name));
        }
    }

    /// <summary>
    /// Enable or disable IPv6
    /// </summary>
    /// <param name="enableIPv6">Whether to enable IPv6</param>
    public void SetIPv6(bool enableIPv6)
    {
        if (EnableIPv6 != enableIPv6)
        {
            EnableIPv6 = enableIPv6;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new NetworkConfigurationUpdatedEvent(Id, Name));
        }
    }

    /// <summary>
    /// Connect a container to this network
    /// </summary>
    /// <param name="containerId">Container to connect</param>
    public void ConnectContainer(ContainerId containerId)
    {
        ArgumentNullException.ThrowIfNull(containerId);

        if (_connectedContainers.Contains(containerId))
            return; // Already connected

        _connectedContainers.Add(containerId);
        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new ContainerConnectedToNetworkEvent(Id, Name, containerId));
    }

    /// <summary>
    /// Disconnect a container from this network
    /// </summary>
    /// <param name="containerId">Container to disconnect</param>
    public void DisconnectContainer(ContainerId containerId)
    {
        ArgumentNullException.ThrowIfNull(containerId);

        if (_connectedContainers.Remove(containerId))
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new ContainerDisconnectedFromNetworkEvent(Id, Name, containerId));
        }
    }

    /// <summary>
    /// Check if a container is connected to this network
    /// </summary>
    /// <param name="containerId">Container to check</param>
    /// <returns>True if container is connected</returns>
    public bool IsContainerConnected(ContainerId containerId)
    {
        return containerId != null && _connectedContainers.Contains(containerId);
    }

    /// <summary>
    /// Update network description
    /// </summary>
    /// <param name="description">New description</param>
    public void UpdateDescription(string? description)
    {
        if (Description != description)
        {
            Description = description;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new NetworkUpdatedEvent(Id, Name));
        }
    }

    /// <summary>
    /// Mark network for removal
    /// </summary>
    public void MarkForRemoval()
    {
        if (IsSystemNetwork)
            throw new InvalidOperationException("Cannot remove system networks");

        if (_connectedContainers.Any())
            throw new InvalidOperationException("Cannot remove network with connected containers");

        AddDomainEvent(new NetworkMarkedForRemovalEvent(Id, Name));
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Clear all domain events
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Add domain event
    /// </summary>
    /// <param name="domainEvent">Domain event to add</param>
    private void AddDomainEvent(DomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
}
