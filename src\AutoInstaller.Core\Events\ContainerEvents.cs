using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Events;

// Container Events
/// <summary>
/// Event raised when a container is created
/// </summary>
/// <param name="ContainerId">Container identifier</param>
/// <param name="Name">Container name</param>
/// <param name="ImageTag">Image tag used</param>
public sealed record ContainerCreatedEvent(
    ContainerId ContainerId,
    string Name,
    ImageTag ImageTag
) : DomainEvent;

/// <summary>
/// Event raised when a container is started
/// </summary>
/// <param name="ContainerId">Container identifier</param>
/// <param name="Name">Container name</param>
public sealed record ContainerStartedEvent(
    ContainerId ContainerId,
    string Name
) : DomainEvent;

/// <summary>
/// Event raised when a container is stopped
/// </summary>
/// <param name="ContainerId">Container identifier</param>
/// <param name="Name">Container name</param>
public sealed record ContainerStoppedEvent(
    ContainerId ContainerId,
    string Name
) : DomainEvent;

/// <summary>
/// Event raised when a container is removed
/// </summary>
/// <param name="ContainerId">Container identifier</param>
/// <param name="Name">Container name</param>
public sealed record ContainerRemovedEvent(
    ContainerId ContainerId,
    string Name
) : DomainEvent;

/// <summary>
/// Event raised when a container status changes
/// </summary>
/// <param name="ContainerId">Container identifier</param>
/// <param name="Name">Container name</param>
/// <param name="PreviousStatus">Previous status</param>
/// <param name="NewStatus">New status</param>
public sealed record ContainerStatusChangedEvent(
    ContainerId ContainerId,
    string Name,
    ContainerStatus PreviousStatus,
    ContainerStatus NewStatus
) : DomainEvent;

// Image Events
/// <summary>
/// Event raised when an image is created
/// </summary>
/// <param name="ImageId">Image identifier</param>
/// <param name="Repository">Image repository</param>
/// <param name="Tag">Image tag</param>
/// <param name="Size">Image size</param>
public sealed record ImageCreatedEvent(
    ImageId ImageId,
    string Repository,
    string Tag,
    long Size
) : DomainEvent;

/// <summary>
/// Event raised when a tag is added to an image
/// </summary>
/// <param name="ImageId">Image identifier</param>
/// <param name="Repository">Image repository</param>
/// <param name="Tag">Tag added</param>
public sealed record ImageTagAddedEvent(
    ImageId ImageId,
    string Repository,
    string Tag
) : DomainEvent;

/// <summary>
/// Event raised when a tag is removed from an image
/// </summary>
/// <param name="ImageId">Image identifier</param>
/// <param name="Repository">Image repository</param>
/// <param name="Tag">Tag removed</param>
public sealed record ImageTagRemovedEvent(
    ImageId ImageId,
    string Repository,
    string Tag
) : DomainEvent;

/// <summary>
/// Event raised when an image is updated
/// </summary>
/// <param name="ImageId">Image identifier</param>
/// <param name="Repository">Image repository</param>
/// <param name="Tag">Image tag</param>
public sealed record ImageUpdatedEvent(
    ImageId ImageId,
    string Repository,
    string Tag
) : DomainEvent;

/// <summary>
/// Event raised when an image size is updated
/// </summary>
/// <param name="ImageId">Image identifier</param>
/// <param name="Repository">Image repository</param>
/// <param name="Tag">Image tag</param>
/// <param name="OldSize">Previous size</param>
/// <param name="NewSize">New size</param>
public sealed record ImageSizeUpdatedEvent(
    ImageId ImageId,
    string Repository,
    string Tag,
    long OldSize,
    long NewSize
) : DomainEvent;

/// <summary>
/// Event raised when an image is marked for removal
/// </summary>
/// <param name="ImageId">Image identifier</param>
/// <param name="Repository">Image repository</param>
/// <param name="Tag">Image tag</param>
public sealed record ImageMarkedForRemovalEvent(
    ImageId ImageId,
    string Repository,
    string Tag
) : DomainEvent;

// Network Events
/// <summary>
/// Event raised when a network is created
/// </summary>
/// <param name="NetworkId">Network identifier</param>
/// <param name="Name">Network name</param>
/// <param name="Driver">Network driver</param>
public sealed record NetworkCreatedEvent(
    NetworkId NetworkId,
    string Name,
    NetworkDriver Driver
) : DomainEvent;

/// <summary>
/// Event raised when a network configuration is updated
/// </summary>
/// <param name="NetworkId">Network identifier</param>
/// <param name="Name">Network name</param>
public sealed record NetworkConfigurationUpdatedEvent(
    NetworkId NetworkId,
    string Name
) : DomainEvent;

/// <summary>
/// Event raised when a network is updated
/// </summary>
/// <param name="NetworkId">Network identifier</param>
/// <param name="Name">Network name</param>
public sealed record NetworkUpdatedEvent(
    NetworkId NetworkId,
    string Name
) : DomainEvent;

/// <summary>
/// Event raised when a network is marked for removal
/// </summary>
/// <param name="NetworkId">Network identifier</param>
/// <param name="Name">Network name</param>
public sealed record NetworkMarkedForRemovalEvent(
    NetworkId NetworkId,
    string Name
) : DomainEvent;

/// <summary>
/// Event raised when a container is connected to a network
/// </summary>
/// <param name="NetworkId">Network identifier</param>
/// <param name="NetworkName">Network name</param>
/// <param name="ContainerId">Container identifier</param>
public sealed record ContainerConnectedToNetworkEvent(
    NetworkId NetworkId,
    string NetworkName,
    ContainerId ContainerId
) : DomainEvent;

/// <summary>
/// Event raised when a container is disconnected from a network
/// </summary>
/// <param name="NetworkId">Network identifier</param>
/// <param name="NetworkName">Network name</param>
/// <param name="ContainerId">Container identifier</param>
public sealed record ContainerDisconnectedFromNetworkEvent(
    NetworkId NetworkId,
    string NetworkName,
    ContainerId ContainerId
) : DomainEvent;

// Volume Events
/// <summary>
/// Event raised when a volume is created
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="Name">Volume name</param>
/// <param name="Driver">Volume driver</param>
public sealed record VolumeCreatedEvent(
    VolumeId VolumeId,
    string Name,
    string Driver
) : DomainEvent;

/// <summary>
/// Event raised when a volume is mounted to a container
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="VolumeName">Volume name</param>
/// <param name="ContainerId">Container identifier</param>
public sealed record VolumeMountedEvent(
    VolumeId VolumeId,
    string VolumeName,
    ContainerId ContainerId
) : DomainEvent;

/// <summary>
/// Event raised when a volume is unmounted from a container
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="VolumeName">Volume name</param>
/// <param name="ContainerId">Container identifier</param>
public sealed record VolumeUnmountedEvent(
    VolumeId VolumeId,
    string VolumeName,
    ContainerId ContainerId
) : DomainEvent;

/// <summary>
/// Event raised when a volume size is updated
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="VolumeName">Volume name</param>
/// <param name="OldSize">Previous size</param>
/// <param name="NewSize">New size</param>
public sealed record VolumeSizeUpdatedEvent(
    VolumeId VolumeId,
    string VolumeName,
    long? OldSize,
    long? NewSize
) : DomainEvent;

/// <summary>
/// Event raised when a volume configuration is updated
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="VolumeName">Volume name</param>
public sealed record VolumeConfigurationUpdatedEvent(
    VolumeId VolumeId,
    string VolumeName
) : DomainEvent;

/// <summary>
/// Event raised when a volume is updated
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="VolumeName">Volume name</param>
public sealed record VolumeUpdatedEvent(
    VolumeId VolumeId,
    string VolumeName
) : DomainEvent;

/// <summary>
/// Event raised when a volume mountpoint is updated
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="VolumeName">Volume name</param>
/// <param name="OldMountpoint">Previous mountpoint</param>
/// <param name="NewMountpoint">New mountpoint</param>
public sealed record VolumeMountpointUpdatedEvent(
    VolumeId VolumeId,
    string VolumeName,
    string OldMountpoint,
    string NewMountpoint
) : DomainEvent;

/// <summary>
/// Event raised when a volume is marked for removal
/// </summary>
/// <param name="VolumeId">Volume identifier</param>
/// <param name="VolumeName">Volume name</param>
public sealed record VolumeMarkedForRemovalEvent(
    VolumeId VolumeId,
    string VolumeName
) : DomainEvent;
