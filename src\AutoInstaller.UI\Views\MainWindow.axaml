<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:AutoInstaller.UI.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
        x:Class="AutoInstaller.UI.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Title="{Binding Title}"
        Width="1200" Height="800"
        MinWidth="800" MinHeight="600"
        WindowStartupLocation="CenterScreen">

    <Design.DataContext>
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🐳" FontSize="24" Margin="0,0,8,0" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding Title}" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Background="#2196F3" 
                            Foreground="White" 
                            CornerRadius="4" 
                            Padding="16,8" 
                            Margin="4"
                            Command="{Binding RefreshSystemInfoCommand}"
                            IsEnabled="{Binding !IsBusy}">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" Margin="0,0,4,0"/>
                            <TextBlock Text="Atualizar"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <TabControl Grid.Row="1" 
                    SelectedIndex="{Binding SelectedTabIndex}"
                    Margin="8">
            
            <!-- Docker System Tab -->
            <TabItem Header="Sistema Docker">
                <ScrollViewer>
                    <StackPanel Margin="16">
                        <TextBlock Text="Informações do Sistema Docker" 
                                   FontSize="24" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,16"/>
                        
                        <!-- System Status Card -->
                        <Border Background="White"
                                CornerRadius="8"
                                BoxShadow="0 2 8 0 #40000000"
                                Padding="16"
                                Margin="8">
                            <StackPanel>
                                <TextBlock Text="Status do Sistema" 
                                           FontSize="16" 
                                           FontWeight="SemiBold" 
                                           Margin="0,0,0,8"/>
                                <StackPanel Orientation="Horizontal" Margin="0,8">
                                    <TextBlock Text="✅" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="Docker Disponível" 
                                               Foreground="#4CAF50"
                                               FontWeight="SemiBold"/>
                                </StackPanel>
                                
                                <TextBlock Text="Versão: 24.0.0" Margin="0,8,0,4"/>
                                <TextBlock Text="Sistema: Windows" Margin="0,0,0,4"/>
                                <TextBlock Text="Arquitetura: x64" Margin="0,0,0,4"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Containers Summary Card -->
                        <Border Background="White"
                                CornerRadius="8"
                                BoxShadow="0 2 8 0 #40000000"
                                Padding="16"
                                Margin="8">
                            <StackPanel>
                                <TextBlock Text="Resumo de Containers" 
                                           FontSize="16" 
                                           FontWeight="SemiBold" 
                                           Margin="0,0,0,8"/>
                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Containers.Count}" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Text="Total" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                        <TextBlock Text="0" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   Foreground="#4CAF50"
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Text="Executando" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                        <TextBlock Text="0" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   Foreground="#FF9800"
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Text="Pausados" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                        <TextBlock Text="0" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   Foreground="#9E9E9E"
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Text="Parados" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>
                        
                        <!-- Images Summary Card -->
                        <Border Background="White"
                                CornerRadius="8"
                                BoxShadow="0 2 8 0 #40000000"
                                Padding="16"
                                Margin="8">
                            <StackPanel>
                                <TextBlock Text="Resumo de Imagens" 
                                           FontSize="16" 
                                           FontWeight="SemiBold" 
                                           Margin="0,0,0,8"/>
                                <StackPanel Orientation="Horizontal" Margin="0,8">
                                    <TextBlock Text="{Binding Images.Count}" 
                                               FontSize="24" 
                                               FontWeight="Bold" 
                                               Foreground="#2196F3"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="imagens disponíveis" VerticalAlignment="Center"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- Containers Tab -->
            <TabItem Header="Containers">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Containers Toolbar -->
                    <Border Grid.Row="0" Background="#F5F5F5" Padding="16">
                        <StackPanel Orientation="Horizontal">
                            <Button Background="#2196F3" 
                                    Foreground="White" 
                                    CornerRadius="4" 
                                    Padding="16,8" 
                                    Margin="4"
                                    Command="{Binding RefreshContainersCommand}"
                                    IsEnabled="{Binding !IsBusy}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🔄" Margin="0,0,4,0"/>
                                    <TextBlock Text="Atualizar"/>
                                </StackPanel>
                            </Button>
                            
                            <Button Background="#4CAF50" 
                                    Foreground="White" 
                                    CornerRadius="4" 
                                    Padding="16,8" 
                                    Margin="4"
                                    Command="{Binding CreateContainerCommand}"
                                    IsEnabled="{Binding !IsBusy}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➕" Margin="0,0,4,0"/>
                                    <TextBlock Text="Criar Container"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Border>
                    
                    <!-- Containers List -->
                    <ScrollViewer Grid.Row="1" Margin="8">
                        <ItemsControl ItemsSource="{Binding Containers}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="White"
                                            CornerRadius="8"
                                            BoxShadow="0 2 8 0 #40000000"
                                            Padding="16"
                                            Margin="8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0">
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                                    <TextBlock Text="📦" FontSize="16" Margin="0,0,8,0"/>
                                                    <TextBlock Text="{Binding Name}" 
                                                               FontWeight="Bold" 
                                                               FontSize="16"/>
                                                    <TextBlock Text="{Binding StatusDisplayName}" 
                                                               Foreground="{Binding StatusColor}"
                                                               FontWeight="SemiBold"
                                                               Margin="16,0,0,0"/>
                                                </StackPanel>
                                                
                                                <TextBlock Text="{Binding ImageTag}" 
                                                           Foreground="#666" 
                                                           Margin="0,0,0,4"/>
                                                
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="Criado:" FontWeight="SemiBold" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding CreatedAt}"/>
                                                    <TextBlock Text="•" Margin="8,0"/>
                                                    <TextBlock Text="Portas:" FontWeight="SemiBold" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding PortMappingsText}"/>
                                                </StackPanel>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                                <Button Background="#4CAF50" 
                                                        Foreground="White" 
                                                        CornerRadius="4" 
                                                        Padding="12,6" 
                                                        Margin="2"
                                                        Command="{Binding $parent[Window].DataContext.StartContainerCommand}"
                                                        CommandParameter="{Binding}"
                                                        IsVisible="{Binding CanStart}">
                                                    <TextBlock Text="▶️ Iniciar"/>
                                                </Button>
                                                
                                                <Button Background="#FF9800" 
                                                        Foreground="White" 
                                                        CornerRadius="4" 
                                                        Padding="12,6" 
                                                        Margin="2"
                                                        Command="{Binding $parent[Window].DataContext.StopContainerCommand}"
                                                        CommandParameter="{Binding}"
                                                        IsVisible="{Binding CanStop}">
                                                    <TextBlock Text="⏹️ Parar"/>
                                                </Button>
                                                
                                                <Button Background="#F44336" 
                                                        Foreground="White" 
                                                        CornerRadius="4" 
                                                        Padding="12,6" 
                                                        Margin="2"
                                                        Command="{Binding $parent[Window].DataContext.RemoveContainerCommand}"
                                                        CommandParameter="{Binding}"
                                                        IsVisible="{Binding CanRemove}">
                                                    <TextBlock Text="🗑️ Remover"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- Images Tab -->
            <TabItem Header="Imagens">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Images Toolbar -->
                    <Border Grid.Row="0" Background="#F5F5F5" Padding="16">
                        <StackPanel Orientation="Horizontal">
                            <Button Background="#2196F3" 
                                    Foreground="White" 
                                    CornerRadius="4" 
                                    Padding="16,8" 
                                    Margin="4"
                                    Command="{Binding RefreshImagesCommand}"
                                    IsEnabled="{Binding !IsBusy}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🔄" Margin="0,0,4,0"/>
                                    <TextBlock Text="Atualizar"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Border>
                    
                    <!-- Images List -->
                    <ScrollViewer Grid.Row="1" Margin="8">
                        <ItemsControl ItemsSource="{Binding Images}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="White"
                                            CornerRadius="8"
                                            BoxShadow="0 2 8 0 #40000000"
                                            Padding="16"
                                            Margin="8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0">
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                                    <TextBlock Text="💿" FontSize="16" Margin="0,0,8,0"/>
                                                    <TextBlock Text="{Binding FullName}" 
                                                               FontWeight="Bold" 
                                                               FontSize="16"/>
                                                    <TextBlock Text="{Binding StatusText}" 
                                                               Foreground="{Binding StatusColor}"
                                                               FontWeight="SemiBold"
                                                               Margin="16,0,0,0"/>
                                                </StackPanel>
                                                
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="Tamanho:" FontWeight="SemiBold" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding FormattedSize}"/>
                                                    <TextBlock Text="•" Margin="8,0"/>
                                                    <TextBlock Text="Criada:" FontWeight="SemiBold" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding CreatedAt}"/>
                                                    <TextBlock Text="•" Margin="8,0"/>
                                                    <TextBlock Text="Registry:" FontWeight="SemiBold" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding Registry}"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                                <Border Background="#E0E0E0"
                                                        Padding="8,4"
                                                        CornerRadius="12">
                                                    <TextBlock Text="Imagem"
                                                               FontSize="12"/>
                                                </Border>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <!-- Success Message -->
                    <TextBlock Text="{Binding SuccessMessage}"
                               Foreground="#4CAF50"
                               FontWeight="SemiBold"
                               IsVisible="{Binding SuccessMessage, Converter={x:Static ObjectConverters.IsNotNull}}"/>

                    <!-- Error Message -->
                    <TextBlock Text="{Binding ErrorMessage}"
                               Foreground="#F44336"
                               FontWeight="SemiBold"
                               IsVisible="{Binding ErrorMessage, Converter={x:Static ObjectConverters.IsNotNull}}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <!-- Busy Indicator -->
                    <StackPanel Orientation="Horizontal" IsVisible="{Binding IsBusy}">
                        <TextBlock Text="⏳" Margin="0,0,4,0"/>
                        <TextBlock Text="Processando..." FontStyle="Italic"/>
                    </StackPanel>
                    
                    <!-- Ready Indicator -->
                    <StackPanel Orientation="Horizontal" IsVisible="{Binding !IsBusy}">
                        <TextBlock Text="✅" Margin="0,0,4,0"/>
                        <TextBlock Text="Pronto"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
