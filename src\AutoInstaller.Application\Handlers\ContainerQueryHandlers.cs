using MediatR;
using Microsoft.Extensions.Logging;
using AutoInstaller.Application.Queries;
using AutoInstaller.Core.Interfaces.Repositories;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Entities;

namespace AutoInstaller.Application.Handlers;

/// <summary>
/// Handler for getting container by ID
/// </summary>
public sealed class GetContainerByIdHandler : IRequestHandler<GetContainerByIdQuery, ContainerDto?>
{
    private readonly IContainerRepository _containerRepository;
    private readonly ILogger<GetContainerByIdHandler> _logger;

    public GetContainerByIdHandler(
        IContainerRepository containerRepository,
        ILogger<GetContainerByIdHandler> logger)
    {
        _containerRepository = containerRepository;
        _logger = logger;
    }

    public async Task<ContainerDto?> Handle(GetContainerByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting container by ID: {ContainerId}", request.ContainerId);

            var containerId = ContainerId.From(request.ContainerId);
            var container = await _containerRepository.GetByIdAsync(containerId, cancellationToken);

            return container != null ? MapToDto(container) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get container by ID {ContainerId}: {Error}", 
                request.ContainerId, ex.Message);
            return null;
        }
    }

    private static ContainerDto MapToDto(Container container)
    {
        return new ContainerDto
        {
            Id = container.Id.Value,
            Name = container.Name,
            ImageTag = container.ImageTag.Value,
            Status = container.Status.ToString(),
            StatusDisplayName = container.Status.GetDisplayName(),
            StatusColor = container.Status.GetStatusColor(),
            CreatedAt = container.CreatedAt,
            UpdatedAt = container.UpdatedAt,
            Description = container.Description,
            PortMappings = container.PortMappings.Select(MapPortMappingToDto).ToArray(),
            VolumeMounts = container.VolumeMounts.Select(MapVolumeMountToDto).ToArray(),
            EnvironmentVariables = container.EnvironmentVariables.Select(MapEnvironmentVariableToDto).ToArray(),
            Networks = Array.Empty<string>() // TODO: Implement network mapping
        };
    }

    private static PortMappingDto MapPortMappingToDto(PortMapping portMapping)
    {
        return new PortMappingDto
        {
            HostPort = portMapping.HostPort,
            ContainerPort = portMapping.ContainerPort,
            Protocol = portMapping.Protocol.ToString(),
            HostIP = portMapping.HostIP,
            DisplayString = portMapping.ToDockerString()
        };
    }

    private static VolumeMountDto MapVolumeMountToDto(VolumeMount volumeMount)
    {
        return new VolumeMountDto
        {
            Source = volumeMount.Source,
            ContainerPath = volumeMount.ContainerPath,
            Type = volumeMount.Type.ToString(),
            IsReadOnly = volumeMount.IsReadOnly,
            DisplayString = volumeMount.ToDockerString()
        };
    }

    private static EnvironmentVariableDto MapEnvironmentVariableToDto(EnvironmentVariable envVar)
    {
        return new EnvironmentVariableDto
        {
            Name = envVar.Name,
            Value = envVar.GetDisplayValue(),
            IsSensitive = envVar.IsSensitive,
            DisplayString = envVar.ToDisplayString()
        };
    }
}

/// <summary>
/// Handler for getting container by name
/// </summary>
public sealed class GetContainerByNameHandler : IRequestHandler<GetContainerByNameQuery, ContainerDto?>
{
    private readonly IContainerRepository _containerRepository;
    private readonly ILogger<GetContainerByNameHandler> _logger;

    public GetContainerByNameHandler(
        IContainerRepository containerRepository,
        ILogger<GetContainerByNameHandler> logger)
    {
        _containerRepository = containerRepository;
        _logger = logger;
    }

    public async Task<ContainerDto?> Handle(GetContainerByNameQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting container by name: {ContainerName}", request.Name);

            var container = await _containerRepository.GetByNameAsync(request.Name, cancellationToken);

            return container != null ? MapToDto(container) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get container by name {ContainerName}: {Error}", 
                request.Name, ex.Message);
            return null;
        }
    }

    private static ContainerDto MapToDto(Container container)
    {
        return new ContainerDto
        {
            Id = container.Id.Value,
            Name = container.Name,
            ImageTag = container.ImageTag.Value,
            Status = container.Status.ToString(),
            StatusDisplayName = container.Status.GetDisplayName(),
            StatusColor = container.Status.GetStatusColor(),
            CreatedAt = container.CreatedAt,
            UpdatedAt = container.UpdatedAt,
            Description = container.Description,
            PortMappings = container.PortMappings.Select(MapPortMappingToDto).ToArray(),
            VolumeMounts = container.VolumeMounts.Select(MapVolumeMountToDto).ToArray(),
            EnvironmentVariables = container.EnvironmentVariables.Select(MapEnvironmentVariableToDto).ToArray(),
            Networks = Array.Empty<string>()
        };
    }

    private static PortMappingDto MapPortMappingToDto(PortMapping portMapping)
    {
        return new PortMappingDto
        {
            HostPort = portMapping.HostPort,
            ContainerPort = portMapping.ContainerPort,
            Protocol = portMapping.Protocol.ToString(),
            HostIP = portMapping.HostIP,
            DisplayString = portMapping.ToDockerString()
        };
    }

    private static VolumeMountDto MapVolumeMountToDto(VolumeMount volumeMount)
    {
        return new VolumeMountDto
        {
            Source = volumeMount.Source,
            ContainerPath = volumeMount.ContainerPath,
            Type = volumeMount.Type.ToString(),
            IsReadOnly = volumeMount.IsReadOnly,
            DisplayString = volumeMount.ToDockerString()
        };
    }

    private static EnvironmentVariableDto MapEnvironmentVariableToDto(EnvironmentVariable envVar)
    {
        return new EnvironmentVariableDto
        {
            Name = envVar.Name,
            Value = envVar.GetDisplayValue(),
            IsSensitive = envVar.IsSensitive,
            DisplayString = envVar.ToDisplayString()
        };
    }
}

/// <summary>
/// Handler for getting all containers
/// </summary>
public sealed class GetAllContainersHandler : IRequestHandler<GetAllContainersQuery, IReadOnlyList<ContainerDto>>
{
    private readonly IContainerRepository _containerRepository;
    private readonly ILogger<GetAllContainersHandler> _logger;

    public GetAllContainersHandler(
        IContainerRepository containerRepository,
        ILogger<GetAllContainersHandler> logger)
    {
        _containerRepository = containerRepository;
        _logger = logger;
    }

    public async Task<IReadOnlyList<ContainerDto>> Handle(GetAllContainersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting all containers (IncludeStopped: {IncludeStopped}, IncludeRemoved: {IncludeRemoved})", 
                request.IncludeStopped, request.IncludeRemoved);

            var containers = await _containerRepository.GetAllAsync(cancellationToken);

            // Apply filters
            var filteredContainers = containers.AsEnumerable();

            if (!request.IncludeStopped)
            {
                filteredContainers = filteredContainers.Where(c => c.Status != ContainerStatus.Stopped && c.Status != ContainerStatus.Exited);
            }

            if (!request.IncludeRemoved)
            {
                filteredContainers = filteredContainers.Where(c => c.Status != ContainerStatus.Removed);
            }

            return filteredContainers.Select(MapToDto).ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all containers: {Error}", ex.Message);
            return Array.Empty<ContainerDto>();
        }
    }

    private static ContainerDto MapToDto(Container container)
    {
        return new ContainerDto
        {
            Id = container.Id.Value,
            Name = container.Name,
            ImageTag = container.ImageTag.Value,
            Status = container.Status.ToString(),
            StatusDisplayName = container.Status.GetDisplayName(),
            StatusColor = container.Status.GetStatusColor(),
            CreatedAt = container.CreatedAt,
            UpdatedAt = container.UpdatedAt,
            Description = container.Description,
            PortMappings = container.PortMappings.Select(MapPortMappingToDto).ToArray(),
            VolumeMounts = container.VolumeMounts.Select(MapVolumeMountToDto).ToArray(),
            EnvironmentVariables = container.EnvironmentVariables.Select(MapEnvironmentVariableToDto).ToArray(),
            Networks = Array.Empty<string>()
        };
    }

    private static PortMappingDto MapPortMappingToDto(PortMapping portMapping)
    {
        return new PortMappingDto
        {
            HostPort = portMapping.HostPort,
            ContainerPort = portMapping.ContainerPort,
            Protocol = portMapping.Protocol.ToString(),
            HostIP = portMapping.HostIP,
            DisplayString = portMapping.ToDockerString()
        };
    }

    private static VolumeMountDto MapVolumeMountToDto(VolumeMount volumeMount)
    {
        return new VolumeMountDto
        {
            Source = volumeMount.Source,
            ContainerPath = volumeMount.ContainerPath,
            Type = volumeMount.Type.ToString(),
            IsReadOnly = volumeMount.IsReadOnly,
            DisplayString = volumeMount.ToDockerString()
        };
    }

    private static EnvironmentVariableDto MapEnvironmentVariableToDto(EnvironmentVariable envVar)
    {
        return new EnvironmentVariableDto
        {
            Name = envVar.Name,
            Value = envVar.GetDisplayValue(),
            IsSensitive = envVar.IsSensitive,
            DisplayString = envVar.ToDisplayString()
        };
    }
}
