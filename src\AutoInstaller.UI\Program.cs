using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.ReactiveUI;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Threading.Tasks;

namespace AutoInstaller.UI;

/// <summary>
/// Entry point for the Auto-Installer Desktop application
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Exit code</returns>
    public static int Main(string[] args)
    {
        try
        {
            // Configure Serilog early
            ConfigureLogging();
            
            Log.Information("Starting Auto-Installer Desktop application");
            
            // Build and run the application
            var app = BuildAvaloniaApp();
            
            if (args.Length > 0 && args[0] == "--drm")
            {
                // Run with DRM support
                return app.StartWithClassicDesktopLifetime(args);
            }
            else
            {
                // Standard run
                return app.StartWithClassicDesktopLifetime(args);
            }
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// Configure Avalonia application
    /// </summary>
    /// <returns>Configured AppBuilder</returns>
    public static AppBuilder BuildAvaloniaApp()
        => AppBuilder.Configure<App>()
            .UsePlatformDetect()
            .WithInterFont()
            .LogToTrace()
            .UseReactiveUI();

    /// <summary>
    /// Configure Serilog logging
    /// </summary>
    private static void ConfigureLogging()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}
